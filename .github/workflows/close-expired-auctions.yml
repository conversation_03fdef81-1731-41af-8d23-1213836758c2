name: Close Expired Auctions

on:
  workflow_dispatch: # Allows manual triggering
  schedule:
    # Runs every 5 minutes
    - cron: '*/5 * * * *'

jobs:
  close_auctions:
    runs-on: ubuntu-latest
    steps:
      - name: Call cron job endpoint to close expired auctions
        run: |
          curl -X POST \
          -H "Authorization: Bearer ${{ secrets.CRON_SECRET }}" \
          -H "Content-Type: application/json" \
          "${{ secrets.PRODUCTION_URL }}/api/cron/close-auctions"