-- supabase/policies/User/02_user_access_policy.sql

-- 1. Policy: Allow users to view their own data
DROP POLICY IF EXISTS "Allow individual user access to their own data" ON public.user;
CREATE POLICY "Allow individual user access to their own data"
ON public.user
FOR SELECT
USING (auth.uid() = id);

-- 2. Policy: Allow users to update their own data
DROP POLICY IF EXISTS "Allow individual user to update their own data" ON public.user;
CREATE POLICY "Allow individual user to update their own data"
ON public.user
FOR UPDATE
USING (auth.uid() = id)
WITH CHECK (auth.uid() = id);

-- 3. Policy: Grant full access to ADMIN users
DROP POLICY IF EXISTS "Allow full access for ADMIN users" ON public.user;
CREATE POLICY "Allow full access for ADMIN users"
ON public.user
FOR ALL
USING (public.get_user_role() = 'ADMIN')
WITH CHECK (public.get_user_role() = 'ADMIN');