import { PrismaClient, Role, Gender, InsurerCompany } from '@prisma/client';
import { createClient } from '@supabase/supabase-js';

const prisma = new PrismaClient();

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

async function main() {
  console.log('Starting database seeding...');

  const testUsers = [
    {
      email: '<EMAIL>',
      password: 'Abcdef7*',
      role: Role.ACCOUNT_HOLDER,
      phone: '+***********',
      profile: {
        firstName: 'María',
        lastName: '<PERSON>',
      },
    },
    {
      email: '<EMAIL>',
      password: 'Abcdef7*',
      role: Role.BROKER,
      phone: '+***********',
      profile: {
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        registrationClass: 'Clase A',
        registrationKey: 'REG_001_2024',
        registrationDate: new Date('2024-01-15'),
        legalName: '<PERSON><PERSON><PERSON>',
        identifier: 'B12345678',
        isAuthorizedByOther: false,
        isComplementary: false,
        isGroupAgent: false,
      },
    },
    {
      email: '<EMAIL>',
      password: 'Abcdef7*',
      role: Role.BROKER,
      phone: '+***********',
      profile: {
        firstName: 'Ana',
        lastName: 'Rodríguez',
        registrationClass: 'Clase A',
        registrationKey: 'REG_002_2024',
        registrationDate: new Date('2024-02-20'),
        legalName: 'Rodríguez Seguros y Finanzas S.L.',
        identifier: 'B87654321',
        isAuthorizedByOther: false,
        isComplementary: false,
        isGroupAgent: false,
      },
    },
    {
      email: '<EMAIL>',
      password: 'Abcdef7*',
      role: Role.BROKER,
      phone: '+34611223344',
      profile: {
        firstName: 'Laura',
        lastName: 'Gómez',
        registrationClass: 'Clase B',
        registrationKey: 'REG_003_2024',
        registrationDate: new Date('2024-03-10'),
        legalName: 'Gómez Asesores de Seguros',
        identifier: 'C98765432',
        isAuthorizedByOther: true,
        isComplementary: false,
        isGroupAgent: false,
      },
    },
    {
      email: '<EMAIL>',
      password: 'Abcdef7*',
      role: Role.ADMIN,
      phone: '+34911234567',
      profile: {
        firstName: 'Admin',
        lastName: 'User',
      },
    },
  ];

  // To ensure a clean slate, first delete all existing users from Supabase Auth and the public.User table
  await prisma.user.deleteMany({});
  console.log('Deleted all existing users from the public.User table.');
  
  const { data: { users: allUsers }, error: listError } = await supabase.auth.admin.listUsers();

  if (listError) {
    console.error('Error fetching users to delete:', listError);
  } else {
    for (const user of allUsers) {
      const { error: deleteError } = await supabase.auth.admin.deleteUser(user.id);
      if (deleteError) {
        console.error(`Error deleting user ${user.email}:`, deleteError);
      } else {
        console.log(`Deleted existing user ${user.email} from Supabase Auth.`);
      }
    }
  }

  for (const userData of testUsers) {
    // Create user in Supabase Auth
    const { data: newAuthUser, error: newAuthError } = await supabase.auth.admin.createUser({
      email: userData.email,
      password: userData.password,
      phone: userData.phone,
      email_confirm: true, // Auto-confirm user
      phone_confirm: true, // Auto-confirm phone
      user_metadata: {
        display_name: `${userData.profile.firstName} ${userData.profile.lastName}`,
        role: userData.role,
        first_name: userData.profile.firstName,
        last_name: userData.profile.lastName,
        phone: userData.phone,
      },
    });

    if (newAuthError) {
      console.error(`Error creating user ${userData.email} in Supabase Auth:`, newAuthError);
      continue;
    }
    const userId = newAuthUser.user.id;
    console.log(`Created user ${userData.email} in Supabase Auth with ID: ${userId}`);

    // Wait a moment for the trigger to process the user creation
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Check if the user was created by the trigger
    const dbUser = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (dbUser) {
      console.log(`User ${userData.email} was created in the database by the trigger.`);
    } else {
      console.log(`Trigger didn't create user ${userData.email}, creating manually...`);
      // Fallback: create manually if trigger didn't work
      const manualUser = await prisma.user.create({
        data: {
          id: userId,
          email: userData.email,
          phone: userData.phone,
          firstName: userData.profile.firstName,
          lastName: userData.profile.lastName,
          displayName: `${userData.profile.firstName} ${userData.profile.lastName}`,
          role: userData.role,
        },
      });
      console.log(`Manually created user ${manualUser.email} in the database.`);
    }

    // Check if profiles were created by the trigger, create manually if not
    switch (userData.role) {
      case Role.ACCOUNT_HOLDER:
        const accountHolderProfile = await prisma.accountHolderProfile.findUnique({
          where: { userId },
        });
        if (accountHolderProfile) {
          console.log(`Account holder profile for ${userData.email} was created by the trigger.`);
        } else {
          console.log(`Creating account holder profile manually for ${userData.email}...`);
          await prisma.accountHolderProfile.create({
            data: { userId },
          });
          console.log(`Manually created account holder profile for ${userData.email}`);
        }
        break;

      case Role.BROKER:
        const brokerProfile = await prisma.brokerProfile.findUnique({
          where: { userId },
        });
        if (brokerProfile) {
          console.log(`Broker profile for ${userData.email} was created by the trigger.`);
          // Update with real data if it was created with defaults
          const profileData = userData.profile as any;
          await prisma.brokerProfile.update({
            where: { userId },
            data: {
              registrationClass: profileData.registrationClass,
              registrationKey: profileData.registrationKey,
              registrationDate: profileData.registrationDate,
              legalName: profileData.legalName,
              identifier: profileData.identifier,
              isAuthorizedByOther: profileData.isAuthorizedByOther,
              isComplementary: profileData.isComplementary,
              isGroupAgent: profileData.isGroupAgent,
            },
          });
          console.log(`Updated broker profile with real data for ${userData.email}`);
        } else {
          console.log(`Creating broker profile manually for ${userData.email}...`);
          const profileData = userData.profile as any;
          await prisma.brokerProfile.create({
            data: {
              userId,
              registrationClass: profileData.registrationClass,
              registrationKey: profileData.registrationKey,
              registrationDate: profileData.registrationDate,
              legalName: profileData.legalName,
              identifier: profileData.identifier,
              isAuthorizedByOther: profileData.isAuthorizedByOther,
              isComplementary: profileData.isComplementary,
              isGroupAgent: profileData.isGroupAgent,
            },
          });
          console.log(`Manually created broker profile for ${userData.email}`);
        }
        break;

      case Role.ADMIN:
        const adminProfile = await prisma.adminProfile.findUnique({
          where: { userId },
        });
        if (adminProfile) {
          console.log(`Admin profile for ${userData.email} was created by the trigger.`);
        } else {
          console.log(`Creating admin profile manually for ${userData.email}...`);
          await prisma.adminProfile.create({
            data: { userId },
          });
          console.log(`Manually created admin profile for ${userData.email}`);
        }
        break;
    }
  }

  // ============================================================================
  // CREATE COMPREHENSIVE TEST DATA
  // ============================================================================

  console.log('\n🏗️  Creating comprehensive test data...');

  // Get the created users for relationships
  const accountHolderUser = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
    include: { accountHolderProfile: true }
  });

  const broker1User = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
    include: { brokerProfile: true }
  });

  const broker2User = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
    include: { brokerProfile: true }
  });

  const broker3User = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
    include: { brokerProfile: true }
  });

  if (!accountHolderUser?.accountHolderProfile || !broker1User?.brokerProfile || !broker2User?.brokerProfile || !broker3User?.brokerProfile) {
    throw new Error('Required user profiles not found');
  }

  // Create addresses for brokers
  console.log('📍 Creating broker addresses...');
  await prisma.address.create({
    data: {
      brokerId: broker1User.brokerProfile.id,
      street: 'Calle Gran Vía, 45, 3º A',
      city: 'Madrid',
      province: 'MADRID',
      region: 'MADRID',
      country: 'SPAIN',
      postalCode: '28013'
    }
  });

  await prisma.address.create({
    data: {
      brokerId: broker2User.brokerProfile.id,
      street: 'Avenida Diagonal, 123, 2º B',
      city: 'Barcelona',
      province: 'BARCELONA',
      region: 'CATALONIA',
      country: 'SPAIN',
      postalCode: '08028'
    }
  });

  await prisma.address.create({
    data: {
      brokerId: broker3User.brokerProfile.id,
      street: 'Plaza del Ayuntamiento, 1',
      city: 'Valencia',
      province: 'VALENCIA',
      region: 'VALENCIAN_COMMUNITY',
      country: 'SPAIN',
      postalCode: '46002'
    }
  });

  console.log(`✅ Created addresses for ${broker1User.firstName}, ${broker2User.firstName} and ${broker3User.firstName}`);

  // Create assets for the account holder
  console.log('🚗 Creating assets...');
  const carAsset = await prisma.asset.create({
    data: {
      accountHolderId: accountHolderUser.accountHolderProfile.id,
      assetType: 'CAR',
      description: 'Seat León 1.5 TSI FR',
      value: 28500.00,
      vehicleDetails: {
        create: {
          brand: 'Seat',
          model: 'León',
          year: 2022,
          firstRegistrationDate: new Date('2022-03-15'),
          licensePlate: '1234ABC',
          version: '1.5 TSI FR',
          fuelType: 'GASOLINE',
          powerCv: 150,
          chassisNumber: 'VSSZZZKJZHXXXXXX',
          isLeased: false,
          seats: 5,
          garageType: 'PRIVATE',
          usageType: 'PRIVATE_REGULAR',
          kmPerYear: 'FROM_10000_TO_12000'
        }
      }
    }
  });

  const motorcycleAsset = await prisma.asset.create({
    data: {
      accountHolderId: accountHolderUser.accountHolderProfile.id,
      assetType: 'MOTORCYCLE',
      description: 'Yamaha MT-07',
      value: 8500.00,
      vehicleDetails: {
        create: {
          brand: 'Yamaha',
          model: 'MT-07',
          year: 2023,
          firstRegistrationDate: new Date('2023-05-20'),
          licensePlate: '5678DEF',
          version: 'ABS',
          fuelType: 'GASOLINE',
          powerCv: 74.8,
          chassisNumber: 'JYARM321000XXXXXX',
          isLeased: false,
          seats: 2,
          garageType: 'SHARED_GUARDED',
          usageType: 'PRIVATE_OCCASIONAL',
          kmPerYear: 'FROM_4000_TO_6000'
        }
      }
    }
  });

  console.log(`✅ Created car asset: ${carAsset.description} and motorcycle: ${motorcycleAsset.description}`);

  // Create insured parties
  console.log('👥 Creating insured parties...');
  const mainInsuredParty = await prisma.insuredParty.create({
    data: {
      accountHolderId: accountHolderUser.accountHolderProfile.id,
      firstName: 'María',
      lastName: 'García López',
      displayName: 'María García López',
      identification: '12345678A',
      roles: ['POLICYHOLDER','MAIN_DRIVER','OWNER'],
      gender: Gender.FEMALE,
      birthDate: new Date('1985-03-15'),
      driverLicenseNumber: '12345678A',
      driverLicenseIssuedAt: new Date('2003-04-01'),
      address: {
        create: {
          street: 'Calle Alcalá, 123, 4º C',
          city: 'Madrid',
          province: 'MADRID',
          region: 'MADRID',
          country: 'SPAIN',
          postalCode: '28009'
        }
      }
    }
  });

  const additionalDriver = await prisma.insuredParty.create({
    data: {
      accountHolderId: accountHolderUser.accountHolderProfile.id,
      firstName: 'Juan',
      lastName: 'García Martín',
      displayName: 'Juan García Martín',
      identification: '87654321B',
      roles: ['ADDITIONAL_DRIVER'],
      gender: Gender.MALE,
      birthDate: new Date('1982-07-22'),
      driverLicenseNumber: '87654321B',
      driverLicenseIssuedAt: new Date('2000-10-15'),
      address: {
        create: {
          street: 'Calle Alcalá, 123, 4º C',
          city: 'Madrid',
          province: 'MADRID',
          region: 'MADRID',
          country: 'SPAIN',
          postalCode: '28009'
        }
      }
    }
  });

  console.log(`✅ Created insured parties: ${mainInsuredParty.firstName} ${mainInsuredParty.lastName} and ${additionalDriver.firstName} ${additionalDriver.lastName}`);

  // Create some documentation records
  console.log('📄 Creating documentation...');
  const carPolicyDocument = await prisma.documentation.create({
    data: {
      accountHolderId: accountHolderUser.accountHolderProfile.id,
      type: 'POLICY_DOCUMENT',
      url: '/documents/policies/poliza_coche_2024.pdf',
      fileName: 'poliza_coche_2024.pdf',
      fileSize: 2048576, // 2MB
      mimeType: 'application/pdf'
    }
  });

  const motorcyclePolicyDocument = await prisma.documentation.create({
    data: {
      accountHolderId: accountHolderUser.accountHolderProfile.id,
      type: 'POLICY_DOCUMENT',
      url: '/documents/policies/poliza_moto_2024.pdf',
      fileName: 'poliza_moto_2024.pdf',
      fileSize: 1536123, // 1.5MB
      mimeType: 'application/pdf'
    }
  });

  const broker1QuoteDocument = await prisma.documentation.create({
    data: {
      brokerId: broker1User.brokerProfile.id,
      type: 'QUOTE_DOCUMENT',
      url: '/documents/quotes/cotizacion_moto_broker1.pdf',
      fileName: 'cotizacion_moto_broker1.pdf',
      fileSize: 1024768, // 1MB
      mimeType: 'application/pdf'
    }
  });

  const broker2QuoteDocument = await prisma.documentation.create({
    data: {
      brokerId: broker2User.brokerProfile.id,
      type: 'QUOTE_DOCUMENT',
      url: '/documents/quotes/cotizacion_moto_broker2.pdf',
      fileName: 'cotizacion_moto_broker2.pdf',
      fileSize: 1124768, // 1.1MB
      mimeType: 'application/pdf'
    }
  });

  const broker3QuoteDocument = await prisma.documentation.create({
    data: {
      brokerId: broker3User.brokerProfile.id,
      type: 'QUOTE_DOCUMENT',
      url: '/documents/quotes/cotizacion_moto_broker3.pdf',
      fileName: 'cotizacion_moto_broker3.pdf',
      fileSize: 924768, // 0.9MB
      mimeType: 'application/pdf'
    }
  });

  console.log('✅ Created documentation records');

  const carPolicy = await prisma.policy.create({
    data: {
      documentId: carPolicyDocument.id,
      accountHolderId: accountHolderUser.accountHolderProfile.id,
      assetId: carAsset.id,
      policyNumber: 'POL-CAR-2024-001',
      insurerCompany: 'MAPFRE',
      status: 'ACTIVE',
      isAssetsTypeConfirmed: true,
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-12-31'),
      paymentPeriod: 'ANNUAL',
      premium: 850.00,
      productName: 'Seguro Todo Riesgo Coche',
      termsAccepted: true,
      termsAcceptedAt: new Date('2024-01-01T10:00:00Z'),
      coverages: {
        create: [
          {
            type: 'MANDATORY_LIABILITY',
            customName: 'Responsabilidad Civil Obligatoria',
            limit: 1200000.00,
            deductible: 0.00,
            description: 'Cobertura de responsabilidad civil obligatoria'
          },
          {
            type: 'VEHICLE_DAMAGE',
            customName: 'Daños Propios',
            limit: 28500.00,
            deductible: 300.00,
            description: 'Cobertura de daños al vehículo propio'
          },
          {
            type: 'THEFT',
            customName: 'Robo',
            limit: 28500.00,
            deductible: 150.00,
            description: 'Cobertura contra robo del vehículo'
          }
        ]
      },
      insuredParties: {
        create: [
          { insuredPartyId: mainInsuredParty.id },
          { insuredPartyId: additionalDriver.id }
        ]
      }
    }
  });

  const motorcyclePolicy = await prisma.policy.create({
    data: {
      documentId: motorcyclePolicyDocument.id,
      accountHolderId: accountHolderUser.accountHolderProfile.id,
      assetId: motorcycleAsset.id,
      policyNumber: 'POL-MOTO-2024-002',
      insurerCompany: 'MUTUA_MADRILENA',
      status: 'DRAFT',
      isAssetsTypeConfirmed: false,
      startDate: new Date('2024-08-01'),
      endDate: new Date('2025-07-31'),
      paymentPeriod: 'ANNUAL',
      premium: 420.00,
      productName: 'Seguro Moto Todo Riesgo',
      termsAccepted: true,
      termsAcceptedAt: new Date('2024-08-01T14:30:00Z'),
      coverages: {
        create: [
          {
            type: 'MANDATORY_LIABILITY',
            customName: 'Responsabilidad Civil Obligatoria',
            limit: 600000.00,
            deductible: 0.00,
            description: 'Cobertura de responsabilidad civil obligatoria para motocicletas'
          },
          {
            type: 'THEFT',
            customName: 'Robo',
            limit: 8500.00,
            deductible: 100.00,
            description: 'Cobertura contra robo de la motocicleta'
          },
          {
            type: 'MOTORCYCLE_GEAR',
            customName: 'Equipamiento Motero',
            limit: 1500.00,
            deductible: 50.00,
            description: 'Cobertura del equipamiento de protección'
          }
        ]
      },
      insuredParties: {
        create: [
          { insuredPartyId: mainInsuredParty.id }
        ]
      }
    }
  });

  console.log(`✅ Created car policy: POL-CAR-2024-001 and motorcycle policy: POL-MOTO-2024-002`);

  // Create auctions
  console.log('🏛️ Creating auctions...');
  const motorcycleAuction = await prisma.auction.create({
    data: {
      accountHolderId: accountHolderUser.accountHolderProfile.id,
      policyId: motorcyclePolicy.id, // Use motorcycle policy for auction
      status: 'OPEN',
      startDate: new Date(),
      endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      workingHoursClosedAt: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
      maxWinners: 3,
      minWinners: 1
    }
  });

  console.log(`✅ Created auction for motorcycle policy: ${motorcycleAuction.id}`);

  // Update documentation with auction id
  await prisma.documentation.update({
    where: { id: motorcyclePolicyDocument.id },
    data: { relatedAuctionId: motorcycleAuction.id }
  });
  await prisma.documentation.update({
    where: { id: broker1QuoteDocument.id },
    data: { relatedAuctionId: motorcycleAuction.id }
  });
  await prisma.documentation.update({
    where: { id: broker2QuoteDocument.id },
    data: { relatedAuctionId: motorcycleAuction.id }
  });
  await prisma.documentation.update({
    where: { id: broker3QuoteDocument.id },
    data: { relatedAuctionId: motorcycleAuction.id }
  });

  console.log('✅ Updated documentation with auction ID');

  // Create bids from brokers
  console.log('💰 Creating bids...');
  const broker1Bid = await prisma.bid.create({
    data: {
      auctionId: motorcycleAuction.id,
      brokerId: broker1User.brokerProfile.id,
      amount: 380.00, // Lower than original premium
      documentId: broker1QuoteDocument.id
    }
  });

  const broker2Bid = await prisma.bid.create({
    data: {
      auctionId: motorcycleAuction.id,
      brokerId: broker2User.brokerProfile.id,
      amount: 395.00, // Slightly higher but still competitive
      documentId: broker2QuoteDocument.id
    }
  });

  const broker3Bid = await prisma.bid.create({
    data: {
      auctionId: motorcycleAuction.id,
      brokerId: broker3User.brokerProfile.id,
      amount: 375.00, // The best offer
      documentId: broker3QuoteDocument.id
    }
  });

  console.log(`✅ Created bids: ${broker1User.firstName} (€${broker1Bid.amount}), ${broker2User.firstName} (€${broker2Bid.amount}), and ${broker3User.firstName} (€${broker3Bid.amount})`);

  // Create auction winners (select the best bids)
  console.log('🏆 Creating auction winners...');
  const winner1 = await prisma.auctionWinner.create({
    data: {
      auctionId: motorcycleAuction.id,
      brokerId: broker3User.brokerProfile.id,
      bidId: broker3Bid.id,
      position: 1 // Best bid (lowest price)
    }
  });

  const winner2 = await prisma.auctionWinner.create({
    data: {
      auctionId: motorcycleAuction.id,
      brokerId: broker1User.brokerProfile.id,
      bidId: broker1Bid.id,
      position: 2 // Second best bid
    }
  });

  const winner3 = await prisma.auctionWinner.create({
    data: {
      auctionId: motorcycleAuction.id,
      brokerId: broker2User.brokerProfile.id,
      bidId: broker2Bid.id,
      position: 3 // Third best bid
    }
  });

  console.log(`✅ Created auction winners: 1st place ${broker3User.firstName}, 2nd place ${broker1User.firstName}, 3rd place ${broker2User.firstName}`);

  // Simulate one winner paying the commission
  console.log('💸 Simulating commission payment...');
  await prisma.auctionCommission.create({
    data: {
      auctionId: motorcycleAuction.id,
      winnerId: winner1.id,
      brokerId: broker3User.brokerProfile.id,
      amount: (motorcyclePolicy.premium?.toNumber() ?? 0) * 0.1, // 10% commission
      status: 'PAID',
      paidAt: new Date(),
      stripePaymentIntentId: 'pi_3P...' // Fake payment intent
    }
  });

  // Update winner to reflect data reveal
  await prisma.auctionWinner.update({
    where: { id: winner1.id },
    data: { contactDataRevealedAt: new Date() }
  });

  console.log(`✅ Commission paid by ${broker3User.firstName} and contact data revealed.`);

  // Create a subscription for a broker
  console.log('💳 Creating broker subscription...');
  await prisma.subscription.create({
    data: {
      brokerId: broker1User.brokerProfile.id,
      stripeSubscriptionId: 'sub_1P...',
      status: 'active',
      currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
    }
  });

  console.log(`✅ Created subscription for ${broker1User.firstName}`);

  console.log('\n🎉 Comprehensive test data creation completed!');
  console.log('📊 Summary of created data:');
  console.log('   - 4 Users (1 account holder, 2 brokers, 1 admin)');
  console.log('   - 2 Assets (1 car, 1 motorcycle) with vehicle details');
  console.log('   - 2 Insured parties with addresses');
  console.log('   - 2 Policies with coverages and insured party relationships');
  console.log('   - 1 Active auction with 3 bids and 3 winners (1 paid commission)');
  console.log('   - 3 Broker addresses');
  console.log('   - 5 Documentation records (2 policies, 3 quotes)');
  console.log('   - 1 Broker subscription');
  console.log('   - Multiple coverage types and relationships');

  console.log('Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('Error during database seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
