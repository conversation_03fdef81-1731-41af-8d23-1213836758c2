import { Separator } from "@/components/ui/separator";
import { ReactNode } from "react";

interface PageLayoutProps {
  title: string;
  description: string;
  children: ReactNode;
  className?: string;
}

export function PageLayout({ title, description, children, className = "" }: PageLayoutProps) {
  return (
    <div className={`space-y-6 ${className}`}>
      <div>
        <h1 className="text-2xl font-semibold tracking-tight">{title}</h1>
        <p className="text-muted-foreground">{description}</p>
      </div>
      <Separator />
      {children}
    </div>
  );
}