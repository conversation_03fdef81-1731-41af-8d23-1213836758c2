/**
 * Shared type definitions for policy-related data structures
 * This centralizes policy types to avoid duplication across components
 */

/**
 * Policy status enum - matches the database enum values
 */
export type PolicyStatus = 
  | "DISPONIBLE" 
  | "PARTICIPANDO" 
  | "GANADA" 
  | "CONFIRMADA" 
  | "DEAL_CONFIRMED" 
  | "FINALIZADA" 
  | "PERDIDAS" 
  | "EXPIRED" 
  | "CANCELED" 
  | "CLOSED";

/**
 * Coverage information structure
 */
export interface PolicyCoverage {
  title: string;
  limit: number | null;
  description: string;
  guaranteeType: string;
}

/**
 * Complete policy data structure used in drawers and detailed views
 * Note: insuredParties are now fetched dynamically via hooks, not passed as props
 */
export interface PolicyData {
  id: string;
  policyNumber: string;
  status: PolicyStatus;
  insurer: string;
  product: string;
  policyType: string;
  validity: string;
  annualPremium: string;
  holderName: string;
  birthDate: string;
  gender: string;
  phone: string;
  email: string;
  // Vehicle fields - all requested fields from user requirements
  vehiclePlate: string;
  vehicleFirstRegistrationDate: string;
  vehicleBrand: string;
  vehicleModel: string;
  vehicleVersion: string;
  vehicleManufacturingYear: string;
  vehicleType: string;
  vehicleFuelType: string;
  vehicleVin: string;
  vehiclePower: string;
  vehicleSeats: string;
  vehicleUsageType: string;
  vehicleGarageType: string;
  vehicleKmPerYear: string;
  vehicleIsLeased: string;
  coverages: PolicyCoverage[];
  // Document information
  document?: {
    id: string;
    fileName: string | null;
    fileSize: number | null;
    mimeType: string | null;
    url: string;
    uploadedAt: string;
  } | null;
}

/**
 * Props for PolicyDetailsDrawer component
 */
export interface PolicyDetailsDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  mode?: "broker" | "account-holder";
  policyData?: PolicyData;
}