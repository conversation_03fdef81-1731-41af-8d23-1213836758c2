import { NextResponse } from 'next/server';
import { closeExpiredAuctions } from '@/features/auctions/services/auction.service';

// This is a simplified example for security. In a real-world scenario,
// the secret should be handled more securely, e.g., through a dedicated secrets manager.
const CRON_SECRET = process.env.CRON_SECRET;

export async function POST(request: Request) {
  const authorization = request.headers.get('Authorization');

  if (authorization !== `Bearer ${CRON_SECRET}`) {
    return new NextResponse('Unauthorized', { status: 401 });
  }

  try {
    const result = await closeExpiredAuctions();
    return NextResponse.json({
      success: true,
      message: `Successfully closed ${result.count} expired auctions.`,
      ...result,
    });
  } catch (error) {
    console.error('Error in cron job to close auctions:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return new NextResponse(JSON.stringify({ success: false, error: errorMessage }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}