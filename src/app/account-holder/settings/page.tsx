import { PageLayout } from "@/components/shared/page-layout";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { ProfileSettingsForm } from "@/features/settings/components/profile-settings-form";
import { PasswordSettingsForm } from "@/features/settings/components/password-settings-form";
import { User, Shield } from "lucide-react";

export default function AccountHolderSettingsPage() {
  return (
    <PageLayout
      title="Configuración"
      description="Administra tu cuenta y preferencias."
    >
      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="profile" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            Perfil
          </TabsTrigger>
          <TabsTrigger value="password" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Contraseña
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="profile" className="space-y-6">
          <ProfileSettingsForm
            title="Información Personal"
            description="Actualiza tu información personal."
          />
        </TabsContent>
        
        <TabsContent value="password" className="space-y-6">
          <PasswordSettingsForm
            title="Cambiar Contraseña"
            description="Actualiza tu contraseña para mantener tu cuenta segura."
          />
        </TabsContent>
        
      </Tabs>
    </PageLayout>
  );
}