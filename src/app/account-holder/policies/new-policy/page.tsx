"use client";

import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { PolicyStepper } from "@/features/account-holder/components/new-policy/policy-stepper";

export default function AccountHolderNewPolicyPage() {
  const router = useRouter();

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="icon"
          onClick={() => router.back()}
          className="hover:bg-primary hover:border-primary hover:text-primary-foreground"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-2xl font-semibold tracking-tight">
          Nueva Póliza
        </h1>
      </div>

      <PolicyStepper />
    </div>
  );
}