"use client";

import { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Clock, Euro, Eye, Users, TrendingUp, Filter } from "lucide-react";
import { cn } from "@/lib/utils";
import { KanbanBoardProvider } from "@/features/broker/components/kanban/kanban-accessibility";
import { KanbanBoard, KanbanBoardExtraMargin } from "@/features/broker/components/kanban/kanban-board";
import { KanbanBoardHeader, KanbanBoardContainer } from "@/features/broker/components/kanban/kanban-header";
import {
  KanbanBoardColumn,
  KanbanBoardColumnSkeleton,
  KanbanBoardColumnHeader,
  KanbanBoardColumnTitle,
  KanbanBoardColumnIconButton,
  KanbanColorCircle,
  KanbanBoardColumnList,
  KanbanBoardColumnListItem
} from "@/features/broker/components/kanban/kanban-column";
import { KanbanBoardCard } from "@/features/broker/components/kanban/kanban-card";
import { AvailableAuction, ParticipatingAuction, WonAuction, ConfirmedAuction, LostAuction, BrokerAuction } from "@/features/broker/types/auction";
import { AvailableAuctionCard } from "@/features/broker/components/available-auction-card";
import { ParticipatingAuctionCard } from "@/features/broker/components/participating-auction-card";
import { WonAuctionCard } from "@/features/broker/components/won-auction-card";
import { ConfirmedAuctionCard } from "@/features/broker/components/confirmed-auction-card";
import { LostAuctionCard } from "@/features/broker/components/lost-auction-card";
import { AuctionCommissionPaymentModal } from "@/features/broker/components/auction-commission-payment-modal";
import { BidPlacementForm } from "@/features/broker/components/bid-placement-form";
import { PolicyDetailsDrawer } from "@/components/shared/PolicyDetailsDrawer";

// Mock data for 5-column Kanban system
const mockAuctionData = {
  disponibles: [
    {
      id: "AUC-2024-001",
      type: "available",
      policyNumber: "POL-MAPFRE-2024-001",
      clientName: "Juan",
      assetType: "CAR",
      brand: "Toyota",
      model: "Corolla",
      year: 2020,
      currentPremium: 450,
      coverageType: "COMPREHENSIVE",
      location: "Madrid",
      timeRemaining: "2h 30m",
      auctionEndTime: new Date(Date.now() + 2.5 * 60 * 60 * 1000), // 2.5 hours from now
      participantCount: 5,
      urgency: "high",
      hasContactInfo: false,
      status: "OPEN",
    },
    {
      id: "AUC-2024-002",
      type: "available",
      policyNumber: "POL-AXA-2024-002",
      clientName: "María",
      assetType: "CAR",
      brand: "Volkswagen",
      model: "Golf",
      year: 2019,
      currentPremium: 380,
      coverageType: "THIRD_PARTY_EXTENDED",
      location: "Barcelona",
      timeRemaining: "5h 15m",
      auctionEndTime: new Date(Date.now() + 5.25 * 60 * 60 * 1000), // 5.25 hours from now
      participantCount: 3,
      urgency: "medium",
      hasContactInfo: false,
      status: "OPEN",
    },
    {
      id: "AUC-2024-003",
      type: "available",
      policyNumber: "POL-MUTUA-2024-003",
      clientName: "Carlos Rodríguez",
      assetType: "MOTORCYCLE",
      brand: "Honda",
      model: "CB600",
      year: 2021,
      currentPremium: 320,
      coverageType: "COMPREHENSIVE",
      location: "Valencia",
      timeRemaining: "1d 3h",
      auctionEndTime: new Date(Date.now() + 27 * 60 * 60 * 1000), // 27 hours from now
      participantCount: 7,
      urgency: "low",
      hasContactInfo: false,
      status: "OPEN",
    },
  ],
  participando: [
    {
      id: "AUC-2024-004",
      type: "participating",
      policyNumber: "POL-ALLIANZ-2024-004",
      clientName: "Ana",
      assetType: "CAR",
      brand: "Seat",
      model: "Ibiza",
      year: 2018,
      currentPremium: 340,
      coverageType: "THIRD_PARTY",
      location: "Sevilla",
      timeRemaining: "8h 45m",
      auctionEndTime: new Date(Date.now() + 8.75 * 60 * 60 * 1000), // 8.75 hours from now
      participantCount: 4,
      urgency: "high",
      hasContactInfo: false,
      myBidAmount: 310,
      myBidRank: 2,
      leadingBid: 305,
      status: "OPEN",
    },
    {
      id: "AUC-2024-005",
      type: "participating",
      policyNumber: "POL-ZURICH-2024-005",
      clientName: "Luis García",
      assetType: "CAR",
      brand: "Ford",
      model: "Focus",
      year: 2022,
      currentPremium: 420,
      coverageType: "COMPREHENSIVE_WITH_DEDUCTIBLE",
      location: "Bilbao",
      timeRemaining: "12h 20m",
      auctionEndTime: new Date(Date.now() + 12.33 * 60 * 60 * 1000), // 12.33 hours from now
      participantCount: 6,
      urgency: "medium",
      hasContactInfo: false,
      myBidAmount: 395,
      myBidRank: 1,
      leadingBid: 395,
      status: "OPEN",
    },
  ],
  ganadas: [
    {
      id: "AUC-2024-006",
      type: "won",
      policyNumber: "POL-GENERALI-2024-006",
      clientName: "Pablo",
      assetType: "CAR",
      brand: "Opel",
      model: "Astra",
      year: 2019,
      currentPremium: 480,
      coverageType: "COMPREHENSIVE",
      location: "Granada",
      timeRemaining: "Ganada",
      participantCount: 8,
      urgency: "completed",
      hasContactInfo: false,
      myBidAmount: 440,
      winningAmount: 440,
      auctionCommissionAmount: 44,
      contactRevealRequired: true,
      status: "AWARDED",
    },
    {
      id: "AUC-2024-007",
      type: "won",
      policyNumber: "POL-LIBERTY-2024-007",
      clientName: "Elena Ruiz",
      assetType: "MOTORCYCLE",
      brand: "Yamaha",
      model: "MT-07",
      year: 2020,
      currentPremium: 390,
      coverageType: "THIRD_PARTY_EXTENDED",
      location: "Murcia",
      timeRemaining: "Finalizada",
      participantCount: 5,
      urgency: "completed",
      hasContactInfo: false,
      myBidAmount: 360,
      winningAmount: 360,
      auctionCommissionAmount: 36,
      contactRevealRequired: true,
      status: "AWARDED",
    },
    {
      id: "AUC-2024-011",
      type: "won",
      policyNumber: "POL-PELAYO-2024-011",
      clientName: "Carmen Vázquez",
      assetType: "CAR",
      brand: "Hyundai",
      model: "i30",
      year: 2021,
      currentPremium: 370,
      coverageType: "COMPREHENSIVE",
      location: "Santander",
      timeRemaining: "Finalizada",
      participantCount: 6,
      urgency: "completed",
      hasContactInfo: true,
      myBidAmount: 340,
      winningAmount: 340,
      auctionCommissionAmount: 34,
      contactRevealRequired: false,
      clientPhone: "+34 612 987 654",
      clientEmail: "<EMAIL>",
      status: "AWARDED",
    },
  ],
  confirmadas: [
    {
      id: "AUC-2024-008",
      type: "confirmed",
      policyNumber: "POL-SANTALUCIA-2024-008",
      clientName: "Miguel Fernández",
      assetType: "CAR",
      brand: "Peugeot",
      model: "308",
      year: 2021,
      currentPremium: 410,
      coverageType: "COMPREHENSIVE_WITH_DEDUCTIBLE",
      location: "Zaragoza",
      timeRemaining: "Confirmada",
      participantCount: 6,
      urgency: "completed",
      hasContactInfo: true,
      myBidAmount: 380,
      winningAmount: 380,
      auctionCommissionAmount: 38,
      contactRevealRequired: false,
      clientPhone: "+34 600 123 456",
      clientEmail: "<EMAIL>",
      licensePlate: "1234-ABC",
      dealConfirmedAt: "2024-01-15T10:30:00Z",
      status: "DEAL_CONFIRMED",
    },
  ],
  perdidas: [
    {
      id: "AUC-2024-009",
      type: "lost",
      policyNumber: "POL-CATALANA-2024-009",
      clientName: "Laura",
      assetType: "CAR",
      brand: "Renault",
      model: "Clio",
      year: 2017,
      currentPremium: 260,
      coverageType: "THIRD_PARTY",
      location: "Córdoba",
      timeRemaining: "Finalizada",
      participantCount: 4,
      urgency: "completed",
      hasContactInfo: false,
      myBidAmount: 250,
      winningAmount: 240,
      lostBy: 10,
      status: "CLOSED",
    },
    {
      id: "AUC-2024-010",
      type: "lost",
      policyNumber: "POL-REALE-2024-010",
      clientName: "Roberto Díaz",
      assetType: "MOTORCYCLE",
      brand: "Kawasaki",
      model: "Ninja 650",
      year: 2019,
      currentPremium: 450,
      coverageType: "COMPREHENSIVE",
      location: "Málaga",
      timeRemaining: "Finalizada",
      participantCount: 9,
      urgency: "completed",
      hasContactInfo: false,
      myBidAmount: 420,
      winningAmount: 410,
      lostBy: 10,
      status: "EXPIRED",
    },
  ],
};

type AuctionColumn = "disponibles" | "participando" | "ganadas" | "confirmadas" | "perdidas";

export default function BrokerAuctionsPage() {
  const [selectedColumn, setSelectedColumn] = useState<AuctionColumn | null>(null);
  const [paymentModal, setPaymentModal] = useState<{
    isOpen: boolean;
    auction: WonAuction | null;
  }>({
    isOpen: false,
    auction: null,
  });

  // Policy Details Drawer state
  const [policyDrawer, setPolicyDrawer] = useState<{
    isOpen: boolean;
    auction: BrokerAuction | null;
  }>({
    isOpen: false,
    auction: null,
  });

  // Header state management
  const [activeTab, setActiveTab] = useState('kanban');
  const [searchValue, setSearchValue] = useState('');
  const [sortBy, setSortBy] = useState('');

  // Filter auction data based on search value
  const filterAuctions = (auctions: any[], searchTerm: string) => {
    if (!searchTerm.trim()) return auctions;
    
    const lowercaseSearch = searchTerm.toLowerCase();
    return auctions.filter((auction) => {
      // Search in auction ID
      if (auction.id.toLowerCase().includes(lowercaseSearch)) return true;
      
      // Search in policy number (if available)
      if (auction.policyNumber?.toLowerCase().includes(lowercaseSearch)) return true;
      
      // Search in client name (if available)
      if (auction.clientName?.toLowerCase().includes(lowercaseSearch)) return true;
      
      // Search in client email (if available)
      if (auction.clientEmail?.toLowerCase().includes(lowercaseSearch)) return true;
      
      // Search in license plate (if available)
      if (auction.licensePlate?.toLowerCase().includes(lowercaseSearch)) return true;
      
      return false;
    });
  };

  // Create filtered auction data
  const filteredAuctionData = {
    disponibles: filterAuctions(mockAuctionData.disponibles, searchValue),
    participando: filterAuctions(mockAuctionData.participando, searchValue),
    ganadas: filterAuctions(mockAuctionData.ganadas, searchValue),
    confirmadas: filterAuctions(mockAuctionData.confirmadas, searchValue),
    perdidas: filterAuctions(mockAuctionData.perdidas, searchValue),
  };

  // Column configuration using filtered data counts
  const columnConfig = {
    disponibles: {
      title: "Disponibles",
      circleColor: "blue" as const,
      count: filteredAuctionData.disponibles.length,
    },
    participando: {
      title: "Participando",
      circleColor: "yellow" as const,
      count: filteredAuctionData.participando.length,
    },
    ganadas: {
      title: "Ganadas",
      circleColor: "green" as const,
      count: filteredAuctionData.ganadas.length,
    },
    confirmadas: {
      title: "Confirmadas",
      circleColor: "cyan" as const,
      count: filteredAuctionData.confirmadas.length,
    },
    perdidas: {
      title: "Perdidas",
      circleColor: "red" as const,
      count: filteredAuctionData.perdidas.length,
    },
  };

  // Transform auction data to policy data format for the drawer
  const transformAuctionToPolicyData = (auction: BrokerAuction) => {
    const getStatusFromType = (type: string) => {
      switch (type) {
        case "available":
          return "DISPONIBLE" as const;
        case "participating":
          return "PARTICIPANDO" as const;
        case "won":
          return "GANADA" as const;
        case "confirmed":
          return "CONFIRMADA" as const;
        case "lost":
          return "FINALIZADA" as const;
        default:
          return "DISPONIBLE" as const;
      }
    };

    const getCoverageTypeDisplay = (coverageType: string) => {
      switch (coverageType) {
        case "COMPREHENSIVE":
          return "Todo Riesgo";
        case "THIRD_PARTY":
          return "Terceros";
        case "THIRD_PARTY_EXTENDED":
          return "Terceros Ampliado";
        case "COMPREHENSIVE_WITH_DEDUCTIBLE":
          return "Todo Riesgo con Franquicia";
        default:
          return coverageType;
      }
    };

    // Mock coverages based on coverage type
    const getCoverages = (coverageType: string) => {
      // Base coverages that apply to all policy types
      const baseCoverages = [
        {
          title: "Responsabilidad civil obligatoria",
          guaranteeType: "MANDATORY_LIABILITY",
          limit: 70000000,
          description: "Cubre hasta 70 000 000 € por lesiones corporales y 15 000 000 € por daños materiales a terceros, según la Ley de Responsabilidad Civil y Seguro en la Circulación de Vehículos a Motor."
        },
        {
          title: "Responsabilidad civil voluntaria",
          guaranteeType: "VOLUNTARY_LIABILITY",
          limit: 50000000,
          description: "Aporta hasta 50 000 000 € adicionales cuando la responsabilidad civil obligatoria resulta insuficiente."
        },
        {
          title: "Defensa y reclamación de daños",
          guaranteeType: "LEGAL_DEFENSE",
          limit: 1000,
          description: "Incluye defensa penal y reclamación de daños con un límite de 1 000 € (ilimitado si se utiliza abogado de la aseguradora)."
        },
        {
          title: "Adelanto de indemnización",
          guaranteeType: "ADVANCE_COMPENSATION",
          limit: 6000,
          description: "Pago anticipado de hasta 6 000 € al asegurado mientras se determina la indemnización definitiva tras un siniestro."
        },
        {
          title: "Peritación para reclamar",
          guaranteeType: "LEGAL_REPRESENTATION_EXTENSION",
          limit: 600,
          description: "Cobertura de hasta 600 € para los gastos de peritación necesarios en la reclamación de daños frente a terceros."
        },
        {
          title: "Asistencia en viaje",
          guaranteeType: "TRAVEL_ASSISTANCE",
          limit: null,
          description: "Servicio de rescate, salvamento, traslado del vehículo, reparación in situ y otras prestaciones para vehículo y ocupantes desde el kilómetro 0."
        }
      ];

      if (coverageType.includes("COMPREHENSIVE")) {
        return [
          ...baseCoverages,
          {
            title: "Accidentes del conductor",
            guaranteeType: "DRIVER_ACCIDENTS",
            limit: 25000,
            description: "Indemniza al conductor con hasta 25 000 € por fallecimiento o incapacidad permanente y cubre asistencia sanitaria ilimitada en centros concertados (hasta 3 000 € en otros centros)."
          },
          {
            title: "Rotura de lunas",
            guaranteeType: "GLASS_BREAKAGE",
            limit: null,
            description: "Cubre la reposición de los cristales del vehículo (parabrisas, ventanillas y luneta) en caso de rotura."
          }
        ];
      }

      return baseCoverages;
    };

    // For confirmed auctions, use actual data; for others, use masked data
    const isConfirmed = auction.type === "confirmed";
    const confirmedAuction = isConfirmed ? auction as ConfirmedAuction : null;

    return {
      id: auction.id,
      policyNumber: auction.policyNumber,
      status: getStatusFromType(auction.type),
      insurer: "Compañía de Seguros",
      product: auction.assetType === "CAR" ? "Seguro de Coche" : "Seguro de Moto",
      policyType: getCoverageTypeDisplay(auction.coverageType),
      validity: "12 meses",
      annualPremium: `€${auction.currentPremium}`,
      holderName: auction.clientName,
      birthDate: "01/01/1980", // Mock data
      gender: "No especificado", // Mock data
      phone: isConfirmed && confirmedAuction ? confirmedAuction.clientPhone : "***-***-***",
      email: isConfirmed && confirmedAuction ? confirmedAuction.clientEmail : "***@***.***",
      vehiclePlate: isConfirmed && confirmedAuction ? confirmedAuction.licensePlate : "****-***",
      vehicleBrand: auction.brand,
      vehicleModel: auction.model,
      vehicleVersion: "Versión estándar", // Mock data
      vehicleYear: auction.year.toString(),
      vehicleType: auction.assetType === "CAR" ? "Turismo" : "Motocicleta",
      vehicleUsage: "Particular",
      vehicleKm: "< 15.000 km/año",
      coverages: getCoverages(auction.coverageType),
      insuredParties: [], // Mock data - no insured parties data available in auctions
    };
  };

  // Payment modal handlers
  const handlePayAuctionCommission = (auctionId: string) => {
    const auction = mockAuctionData.ganadas.find(a => a.id === auctionId) as WonAuction;
    if (auction) {
      setPaymentModal({
        isOpen: true,
        auction,
      });
    }
  };

  const handleClosePaymentModal = () => {
    setPaymentModal({
      isOpen: false,
      auction: null,
    });
  };

  const handlePaymentComplete = (auctionId: string) => {
    // TODO: Handle payment completion
    console.log(`Payment completed for auction ${auctionId}`);
    handleClosePaymentModal();
  };

  // Contact action handlers
  const handleCallClient = (phone: string) => {
    window.open(`tel:${phone}`, '_self');
  };

  const handleEmailClient = (email: string) => {
    window.open(`mailto:${email}`, '_self');
  };

  // Policy Details Drawer handlers
  const handleOpenPolicyDrawer = (auction: BrokerAuction) => {
    setPolicyDrawer({
      isOpen: true,
      auction,
    });
  };

  const handleClosePolicyDrawer = () => {
    setPolicyDrawer({
      isOpen: false,
      auction: null,
    });
  };

  // Header handlers
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    // TODO: Implement view switching logic
    console.log('Tab changed to:', value);
  };

  const handleSearchChange = (value: string) => {
    setSearchValue(value);
  };

  const handleSortChange = (value: string) => {
    setSortBy(value);
    // TODO: Implement sorting functionality
    console.log('Sort by:', value);
  };

  const handleFiltersClick = () => {
    // TODO: Implement filters functionality
    console.log('Filters clicked');
  };

  
  

  const renderAuctionCard = (auction: any, columnType: AuctionColumn) => {
    switch (auction.type) {
      case "available":
        return <AvailableAuctionCard key={auction.id} auction={auction as AvailableAuction} onOpenDrawer={(id) => handleOpenPolicyDrawer(auction as BrokerAuction)} isNestedInInteractiveElement={true} />;
      case "participating":
          return <ParticipatingAuctionCard key={auction.id} auction={auction as ParticipatingAuction} onOpenDrawer={(id) => handleOpenPolicyDrawer(auction as BrokerAuction)} isNestedInInteractiveElement={true} />;
      case "won":
          return (
            <WonAuctionCard
              key={auction.id}
              auction={auction as WonAuction}
              onPayAuctionCommission={handlePayAuctionCommission}
              onCallClient={handleCallClient}
              onEmailClient={handleEmailClient}
              onOpenDrawer={(id) => handleOpenPolicyDrawer(auction as BrokerAuction)}
              isNestedInInteractiveElement={true}
            />
          );
      case "confirmed":
        return (
          <ConfirmedAuctionCard
            key={auction.id}
            auction={auction as ConfirmedAuction}
            onManageClient={(auctionId) => {
              console.log(`Managing client for auction ${auctionId}`);
              // Implement client management functionality here
            }}
            onOpenDrawer={(id) => handleOpenPolicyDrawer(auction as BrokerAuction)}
            isNestedInInteractiveElement={true}
          />
        );
      case "lost":
          return <LostAuctionCard key={auction.id} auction={auction as LostAuction} onOpenDrawer={(id) => handleOpenPolicyDrawer(auction as BrokerAuction)} isNestedInInteractiveElement={true} />;
      default:
        return <AvailableAuctionCard key={auction.id} auction={auction as AvailableAuction} onOpenDrawer={(id) => handleOpenPolicyDrawer(auction as BrokerAuction)} isNestedInInteractiveElement={true} />;
    }
  };

  return (
    <div className="flex flex-col space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Subastas</h1>
      </div>

      {/* Kanban Board with Header */}
      <div className="min-h-[600px] border rounded-lg">
        <KanbanBoardProvider>
          <KanbanBoardContainer
            header={
              <KanbanBoardHeader
                activeTab={activeTab}
                onTabChange={handleTabChange}
                searchValue={searchValue}
                onSearchChange={handleSearchChange}
                sortBy={sortBy}
                onSortChange={handleSortChange}
                onFiltersClick={handleFiltersClick}
              />
            }
          >
            <KanbanBoard>
              {(Object.keys(columnConfig) as AuctionColumn[]).map((columnKey) => {
                const config = columnConfig[columnKey];
                return (
                  <KanbanBoardColumn key={columnKey} columnId={columnKey}>
                    {/* Column Header */}
                    <KanbanBoardColumnHeader>
                      <KanbanBoardColumnTitle columnId={columnKey}>
                        <KanbanColorCircle color={config.circleColor} />
                        {config.title}
                      </KanbanBoardColumnTitle>
                      <Badge variant="secondary">{config.count}</Badge>
                    </KanbanBoardColumnHeader>

                    {/* Column Content */}
                    <KanbanBoardColumnList>
                      {filteredAuctionData[columnKey as AuctionColumn].map((auction) => (
                        <KanbanBoardColumnListItem
                          key={auction.id}
                          cardId={auction.id}
                          className="mb-0"
                          onDropOverListItem={(dataTransferData, dropDirection) => {
                            // Handle drag and drop functionality
                            console.log('Drop over list item:', auction.id, dataTransferData, dropDirection);
                          }}
                        >
                          <KanbanBoardCard
                            data={{ id: auction.id }}
                            isActive={false}
                            onClick={() => handleOpenPolicyDrawer(auction as BrokerAuction)}
                          >
                            {renderAuctionCard(auction, columnKey as AuctionColumn)}
                          </KanbanBoardCard>
                        </KanbanBoardColumnListItem>
                      ))}
                      {filteredAuctionData[columnKey as AuctionColumn].length === 0 && (
                        <li className="text-center py-8 text-muted-foreground">
                          <p>No hay subastas en esta columna</p>
                        </li>
                      )}
                    </KanbanBoardColumnList>
                  </KanbanBoardColumn>
                );
              })}
              <KanbanBoardExtraMargin />
            </KanbanBoard>
          </KanbanBoardContainer>
        </KanbanBoardProvider>
      </div>

      {/* AuctionCommission Payment Modal */}
      {paymentModal.auction && (
        <AuctionCommissionPaymentModal
          auction={paymentModal.auction}
          isOpen={paymentModal.isOpen}
          onClose={handleClosePaymentModal}
          onPaymentComplete={handlePaymentComplete}
        />
      )}

      {/* Policy Details Drawer */}
      <PolicyDetailsDrawer
        isOpen={policyDrawer.isOpen}
        onClose={handleClosePolicyDrawer}
        policyData={policyDrawer.auction ? transformAuctionToPolicyData(policyDrawer.auction) : undefined}
      />
    </div>
  );
}