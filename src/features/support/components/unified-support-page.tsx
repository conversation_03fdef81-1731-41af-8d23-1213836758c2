import { PageLayout } from "@/components/shared/page-layout";
import { SupportCard } from "./support-card";
import { supportConfig } from "../config/support.config";
import { UserRole } from "../types/support.types";

interface UnifiedSupportPageProps {
  userRole: UserRole;
  title?: string;
  description?: string;
}

// Default titles and descriptions based on role
const getRoleDefaults = (userRole: UserRole) => {
  switch (userRole) {
    case "ACCOUNT_HOLDER":
      return {
        title: "Soporte",
        description: "¿Necesitas ayuda? Estamos aquí para asistirte."
      };
    case "BROKER":
      return {
        title: "Soporte",
        description: "¿Necesitas ayuda con subastas, comisiones o gestión de clientes? Te ayudamos."
      };
    case "ADMIN":
      return {
        title: "Soporte Técnico - Administración",
        description: "Recursos técnicos y soporte especializado para administradores del sistema."
      };
    default:
      return {
        title: "Soporte",
        description: "¿Necesitas ayuda? Estamos aquí para asistirte."
      };
  }
};

export function UnifiedSupportPage({ userRole, title, description }: UnifiedSupportPageProps) {
  // Get default title and description if not provided
  const defaults = getRoleDefaults(userRole);
  const finalTitle = title || defaults.title;
  const finalDescription = description || defaults.description;
  // Filter contacts and resources based on user role
  const contacts = supportConfig.contacts.filter(contact => 
    contact.roles.includes(userRole)
  );
  
  const resources = supportConfig.resources.filter(resource => 
    resource.roles.includes(userRole)
  );

  return (
    <PageLayout
      title={finalTitle}
      description={finalDescription}
    >
      <div className="space-y-6">
        {/* Contact Cards */}
        {contacts.length > 0 && (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {contacts.map((contact) => (
              <SupportCard
                key={contact.id}
                item={contact}
              />
            ))}
          </div>
        )}

        {/* Resource Cards */}
        {resources.length > 0 && (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {resources.map((resource) => (
              <SupportCard
                key={resource.id}
                item={resource}
              />
            ))}
          </div>
        )}
      </div>
    </PageLayout>
  );
}