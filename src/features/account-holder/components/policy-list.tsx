"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { PageLayout } from "@/components/shared/page-layout";
import { PrimaryButton } from "@/components/shared/primary-button";
import { Plus, Car, Bike, Search, X, Loader2, AlertCircle } from "lucide-react";
import Link from "next/link";
import { PolicyStatus, AssetType } from "@prisma/client";
import { PolicyCard } from "@/features/policies/components/policy-card";
import { useState, useMemo, ChangeEvent, useEffect } from "react";

import { usePolicies, usePolicyCounts } from "../hooks/usePolicies";
import { formatInsurerCompany } from "@/lib/format-insurer";

function LoadingState() {
  return (
    <div className="flex items-center justify-center py-12">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
        <p className="text-gray-600">Cargando pólizas...</p>
      </div>
    </div>
  );
}

function ErrorState({ error, onRetry }: { error: string; onRetry: () => void }) {
  return (
    <Card>
      <CardContent className="p-10 text-center">
        <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Error al cargar pólizas</h3>
        <p className="text-gray-600 mb-6">{error}</p>
        <Button onClick={onRetry} variant="outline">
          Intentar de nuevo
        </Button>
      </CardContent>
    </Card>
  );
}

export function PolicyList() {

  const [filterStatus, setFilterStatus] = useState<
    PolicyStatus | "all" | "attention" | "renew_soon" | "expired"
  >("all");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState<string>("");
  const [filterAssetsType, setFilterAssetsType] = useState<"all" | AssetType>(
    "all"
  );
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);

  // Debounce search term to avoid excessive API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 400); // 400ms delay

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Convert filter status to API format
  const apiStatusFilter = useMemo(() => {
    if (filterStatus === "all") return undefined;
    if (filterStatus === "attention") return "attention";
    if (filterStatus === "renew_soon") return "RENEW_SOON";
    if (filterStatus === "expired") return "EXPIRED";
    return filterStatus;
  }, [filterStatus]);

  // Fetch policies using the new hook
  const {
    data: policiesResponse,
    isLoading,
    error,
    refetch
  } = usePolicies({
    page: currentPage,
    limit: itemsPerPage,
    status: apiStatusFilter,
    assetType: filterAssetsType !== "all" ? filterAssetsType : undefined,
    search: debouncedSearchTerm || undefined,
  });

  // Fetch total counts for all categories (without filters)
  const {
    data: totalCounts,
    isLoading: isLoadingCounts
  } = usePolicyCounts();

  const policies = policiesResponse?.data || [];
  const pagination = policiesResponse?.pagination;

  const handleSearchChange = (event: ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  // Reset to first page when debounced search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchTerm]);

  // Transform server data to match PolicyCard component expectations
  const transformedPolicies = useMemo(() => {
    return policies.map((policy) => {
      const assetInfo = policy.asset?.vehicleDetails
        ? `${policy.asset.vehicleDetails.brand || "Sin marca"} ${policy.asset.vehicleDetails.model || "Sin modelo"} (${policy.asset.vehicleDetails.year || "Sin año"})`
        : policy.asset?.description || "Sin información del activo";

      const insuredName = policy.insuredParties && policy.insuredParties.length > 0
        ? policy.insuredParties.find(party => party.role === "POLICYHOLDER")?.fullName || policy.insuredParties[0]?.fullName || "Sin asegurado"
        : "Sin asegurado";

      return {
        ...policy,
        id: policy.id,
        policyNumber: policy.policyNumber || "Sin número",
        assetInfo,
        insurerName: policy.insurerCompany ? formatInsurerCompany(policy.insurerCompany) : "Sin aseguradora",
        productName: policy.productName || "Sin producto",
        premium: policy.premium || 0,
        status: policy.status || PolicyStatus.DRAFT,
        endDate: policy.endDate ? new Date(policy.endDate) : null,
        insuredName,
        coverageCount: policy.coverages ? policy.coverages.length : 0,
        assetType: (policy.asset?.assetType || policy.type || "CAR") as AssetType,
        baseUrl: "/account-holder",
        // Pass the full policy object
        policyData: policy,
      };
    });
  }, [policies]);

  // Use total counts from the dedicated hook for accurate counts
  const drafts = totalCounts?.drafts || 0;
  const actives = totalCounts?.actives || 0;
  const renewSoon = totalCounts?.renewSoon || 0;
  const expired = totalCounts?.expired || 0;
  const needsAttention = totalCounts?.needsAttention || 0;
  const carPolicies = totalCounts?.carPolicies || 0;
  const motoPolicies = totalCounts?.motoPolicies || 0;
  const totalPolicies = totalCounts?.total || 0;

  const getFilterLabel = (
    status: PolicyStatus | "all" | "attention" | "renew_soon" | "expired"
  ) => {
    switch (status) {
      case "all":
        return `Todas las pólizas (${totalPolicies})`;
      case "ACTIVE":
        return `Activas (${actives})`;
      case "DRAFT":
        return `Borradores (${drafts})`;
      case "RENEW_SOON":
        return `Renovar Pronto (${renewSoon})`;
      case "EXPIRED":
        return `Expiradas (${expired})`;
      case "attention":
        return `Requieren Atención (${needsAttention})`;
      default:
        return `Todas las pólizas (${totalPolicies})`;
    }
  };

  const getButtonClass = (type: "all" | AssetType) => {
    return `flex-grow md:flex-grow-0 ${
      filterAssetsType === type
        ? "bg-primary text-black hover:bg-primary hover:text-black"
        : "bg-white text-black brand-hover"
    } transition-colors duration-200`;
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number(value));
    setCurrentPage(1);
  };

  const handleStatusFilterChange = (
    value: PolicyStatus | "all" | "attention" | "renew_soon" | "expired"
  ) => {
    setFilterStatus(value);
    setCurrentPage(1);
  };

  const handleAssetTypeFilterChange = (type: "all" | AssetType) => {
    setFilterAssetsType(type);
    setCurrentPage(1);
  };

  // Show full loading state only for initial load (when there's no data yet)
  const isInitialLoading = (isLoading && !policiesResponse) || isLoadingCounts;
  const isSearching = isLoading && !!policiesResponse && (searchTerm !== debouncedSearchTerm);

  if (isInitialLoading) {
    return (
      <PageLayout
        title="Mis Pólizas"
        description="Gestiona todas tus pólizas de seguro desde un solo lugar"
      >
        <LoadingState />
      </PageLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <PageLayout
        title="Mis Pólizas"
        description="Gestiona todas tus pólizas de seguro desde un solo lugar"
      >
        <ErrorState 
          error={error instanceof Error ? error.message : "Error desconocido"} 
          onRetry={() => refetch()} 
        />
      </PageLayout>
    );
  }

  return (
    <PageLayout
      title="Mis Pólizas"
      description="Gestiona todas tus pólizas de seguro desde un solo lugar"
    >
      <div className="flex flex-col lg:flex-row gap-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 flex-grow">
          <Card className="border-l-4 border-gray-400">
            <CardContent className="p-4">
              <h3 className="text-sm font-medium text-gray-500">Borradores</h3>
              <p className="text-2xl font-bold">{drafts}</p>
              <p className="text-xs text-gray-500">Pólizas en edición</p>
            </CardContent>
          </Card>
          <Card className="border-l-4 border-green-500">
            <CardContent className="p-4">
              <h3 className="text-sm font-medium text-gray-500">Activas</h3>
              <p className="text-2xl font-bold">{actives}</p>
              <p className="text-xs text-gray-500">Pólizas vigentes</p>
            </CardContent>
          </Card>
          <Card className="border-l-4 border-orange-500">
            <CardContent className="p-4">
              <h3 className="text-sm font-medium text-gray-500">
                Requieren Atención
              </h3>
              <p className="text-2xl font-bold">{needsAttention}</p>
              <p className="text-xs text-gray-500">Por renovar o expiradas</p>
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="space-y-4">
        {/* Search Input and Asset Type Buttons */}
        <div className="flex flex-col md:flex-row gap-4 items-center">
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
            <Input
              placeholder="Buscar por número de póliza, marca, modelo, aseguradora o tomador..."
              className="pl-10 pr-10"
              value={searchTerm}
              onChange={handleSearchChange}
            />
            {isSearching && (
              <Loader2 className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 animate-spin text-gray-400" />
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              className={getButtonClass("all")}
              onClick={() => handleAssetTypeFilterChange("all")}
            >
              Todas {totalPolicies}
            </Button>
            <Button
              variant="outline"
              className={getButtonClass("CAR")}
              onClick={() => handleAssetTypeFilterChange("CAR")}
            >
              <Car className="mr-2 h-4 w-4" /> Coche {carPolicies}
            </Button>
            <Button
              variant="outline"
              className={getButtonClass("MOTORCYCLE")}
              onClick={() => handleAssetTypeFilterChange("MOTORCYCLE")}
            >
              <Bike className="mr-2 h-4 w-4" /> Moto {motoPolicies}
            </Button>
          </div>
        </div>

        {/* Second Row: Status Filter, Clear button, and Create New Policy button */}
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-2">
            <Select
              onValueChange={handleStatusFilterChange}
              value={filterStatus}
            >
              <SelectTrigger className="w-full md:w-auto bg-white">
                <SelectValue placeholder={getFilterLabel(filterStatus)} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all" className="focus:bg-primary focus:text-primary-foreground data-[highlighted]:bg-primary data-[highlighted]:text-primary-foreground">
                  {getFilterLabel("all")}
                </SelectItem>
                <SelectItem value={PolicyStatus.DRAFT} className="focus:bg-primary focus:text-primary-foreground data-[highlighted]:bg-primary data-[highlighted]:text-primary-foreground">
                  {getFilterLabel(PolicyStatus.DRAFT)}
                </SelectItem>
                <SelectItem value={PolicyStatus.ACTIVE} className="focus:bg-primary focus:text-primary-foreground data-[highlighted]:bg-primary data-[highlighted]:text-primary-foreground">
                  {getFilterLabel(PolicyStatus.ACTIVE)}
                </SelectItem>
                <SelectItem value={PolicyStatus.RENEW_SOON} className="focus:bg-primary focus:text-primary-foreground data-[highlighted]:bg-primary data-[highlighted]:text-primary-foreground">
                  {getFilterLabel(PolicyStatus.RENEW_SOON)}
                </SelectItem>
                <SelectItem value={PolicyStatus.EXPIRED} className="focus:bg-primary focus:text-primary-foreground data-[highlighted]:bg-primary data-[highlighted]:text-primary-foreground">
                  {getFilterLabel(PolicyStatus.EXPIRED)}
                </SelectItem>
                <SelectItem value="attention" className="focus:bg-primary focus:text-primary-foreground data-[highlighted]:bg-primary data-[highlighted]:text-primary-foreground">
                  {getFilterLabel("attention")}
                </SelectItem>
              </SelectContent>
            </Select>
            {filterStatus !== "all" && (
              <Button
                size="icon"
                onClick={() => handleStatusFilterChange("all")}
                className="h-9 w-9 bg-primary text-white"
              >
                <X className="h-5 w-5" />
              </Button>
            )}
          </div>
          
          {/* Crear Nueva Subasta Button - positioned on the right */}
          <Link href="/account-holder/policies/new-policy">
            <PrimaryButton>
              <Plus className="h-4 w-4 mr-2" />
              Crear Nueva Subasta
            </PrimaryButton>
          </Link>
        </div>
      </div>

      <div className="space-y-4">
        {/* Pagination Controls - Top */}
        {pagination && pagination.totalCount > 0 && (
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="flex items-center gap-2">
              <Button
                onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
                disabled={!pagination.hasPreviousPage}
                className="bg-primary text-black hover:bg-primary"
              >
                Anterior
              </Button>
              <span className="text-sm text-gray-700">
                Página {pagination.page} de {pagination.totalPages}
              </span>
              <Button
                onClick={() => handlePageChange(Math.min(currentPage + 1, pagination.totalPages))}
                disabled={!pagination.hasNextPage}
                className="bg-primary text-black hover:bg-primary"
              >
                Siguiente
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-700">
                Elementos por página:
              </span>
              <Select
                onValueChange={handleItemsPerPageChange}
                value={String(itemsPerPage)}
              >
                <SelectTrigger className="w-[80px]">
                  <SelectValue placeholder={itemsPerPage} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        {transformedPolicies.length > 0 ? (
          transformedPolicies.map((policy) => (
            <PolicyCard
              key={policy.id}
              policy={policy.policyData} // Pass the full policy object
              id={policy.id}
              policyNumber={policy.policyNumber}
              assetInfo={policy.assetInfo}
              insurerName={policy.insurerName}
              productName={policy.productName}
              premium={policy.premium}
              status={policy.status}
              endDate={policy.endDate}
              insuredName={policy.insuredName}
              coverageCount={policy.coverageCount}
              assetType={policy.assetType}
              baseUrl="/account-holder"
            />
          ))
        ) : (
          <Card>
            <CardContent className="p-10 text-center text-gray-500">
              {debouncedSearchTerm || filterStatus !== "all" || filterAssetsType !== "all"
                ? "No se encontraron pólizas que coincidan con los filtros aplicados."
                : "No tienes pólizas registradas. ¡Comienza creando tu primera póliza!"
              }
            </CardContent>
          </Card>
        )}

        {/* Pagination Controls - Bottom */}
        {pagination && pagination.totalCount > 0 && (
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="flex items-center gap-2">
              <Button
                onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
                disabled={!pagination.hasPreviousPage}
                className="bg-primary text-black hover:bg-primary"
              >
                Anterior
              </Button>
              <span className="text-sm text-gray-700">
                Página {pagination.page} de {pagination.totalPages}
              </span>
              <Button
                onClick={() => handlePageChange(Math.min(currentPage + 1, pagination.totalPages))}
                disabled={!pagination.hasNextPage}
                className="bg-primary text-black hover:bg-primary"
              >
                Siguiente
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-700">
                Elementos por página:
              </span>
              <Select
                onValueChange={handleItemsPerPageChange}
                value={String(itemsPerPage)}
              >
                <SelectTrigger className="w-[80px]">
                  <SelectValue placeholder={itemsPerPage} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )}
      </div>
    </PageLayout>
  );
}