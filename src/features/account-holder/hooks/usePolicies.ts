import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { PolicyStatus } from "@prisma/client";

// Types for the API response
export interface PolicyData {
  id: string;
  policyNumber: string | null;
  status: PolicyStatus | null;
  type: string; // Asset type (CAR, MOTORCYCLE, etc.)
  premium: number | null;
  startDate: Date | null;
  endDate: Date | null;
  productName: string | null;
  insurerCompany: string | null;
  brokerName: string | null;
  createdAt: Date;
  updatedAt: Date;
  asset: {
    id: string;
    assetType: string;
    description: string | null;
    value: number | null;
    vehicleDetails: {
      licensePlate: string | null;
      firstRegistrationDate: Date | null;
      brand: string | null;
      model: string | null;
      version: string | null;
      year: number | null;
      fuelType: string | null;
      chassisNumber: string | null;
      powerCv: number | null;
      seats: number | null;
      usageType: string | null;
      garageType: string | null;
      kmPerYear: string | null;
      isLeased: boolean | null;
    } | null;
  } | null;
  coverages: Array<{
    id: string;
    type: string;
    customName: string | null;
    limit: number | null;
    deductible: number | null;
    description: string | null;
  }>;
  insuredParties: Array<{
    id: string;
    role: string;
    fullName: string;
    firstName: string;
    lastName: string;
    identification: string;
  }>;
  accountHolder: {
    id: string;
    userId: string;
    firstName: string;
    lastName: string;
  } | null;
  broker: {
    id: string;
    legalName: string;
    registrationKey: string;
  } | null;
  document: {
    id: string;
    fileName: string | null;
    fileSize: number | null;
    mimeType: string | null;
    url: string;
    uploadedAt: string;
  } | null;
}

export interface PoliciesResponse {
  data: PolicyData[];
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export interface UsePoliciesParams {
  page?: number;
  limit?: number;
  status?: string;
  assetType?: string;
  search?: string;
}

// Hook for paginated policies (traditional pagination)
export function usePolicies(params: UsePoliciesParams = {}) {
  const { page = 1, limit = 10, status, assetType, search } = params;

  return useQuery<PoliciesResponse>(
    ["policies", "account-holder", { page, limit, status, assetType, search }],
    async (): Promise<PoliciesResponse> => {
      const searchParams = new URLSearchParams();
      searchParams.set("page", page.toString());
      searchParams.set("limit", limit.toString());
      
      if (status) searchParams.set("status", status);
      if (assetType) searchParams.set("assetType", assetType);
      if (search) searchParams.set("search", search);

      const response = await fetch(`/api/account-holder/policies/list?${searchParams.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || "Error al cargar las pólizas");
      }

      return response.json();
    },
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    }
  );
}

// Hook for infinite scroll policies (infinite query)
export function usePoliciesInfinite(params: Omit<UsePoliciesParams, "page"> = {}) {
  const { limit = 10, status, assetType, search } = params;

  return useInfiniteQuery<PoliciesResponse>(
    ["policies", "account-holder", "infinite", { limit, status, assetType, search }],
    async ({ pageParam = 1 }): Promise<PoliciesResponse> => {
      const searchParams = new URLSearchParams();
      searchParams.set("page", pageParam.toString());
      searchParams.set("limit", limit.toString());
      
      if (status) searchParams.set("status", status);
      if (assetType) searchParams.set("assetType", assetType);
      if (search) searchParams.set("search", search);

      const response = await fetch(`/api/account-holder/policies/list?${searchParams.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || "Error al cargar las pólizas");
      }

      return response.json();
    },
    {
      getNextPageParam: (lastPage) => {
        return lastPage.pagination.hasNextPage 
          ? lastPage.pagination.page + 1 
          : undefined;
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    }
  );
}

// Hook for fetching total counts for all policy categories (without filters)
export function usePolicyCounts() {
  return useQuery<{
    total: number;
    drafts: number;
    actives: number;
    renewSoon: number;
    expired: number;
    needsAttention: number;
    carPolicies: number;
    motoPolicies: number;
  }>(
    ["policies", "account-holder", "counts"],
    async () => {
      // Use a high limit to get all policies for accurate counts
      // In a production environment, consider creating a dedicated /counts endpoint
      const response = await fetch(`/api/account-holder/policies/list?page=1&limit=10000`);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || "Error al cargar los conteos de pólizas");
      }

      const data: PoliciesResponse = await response.json();
      const policies = data.data;

      // Calculate counts from all policies
      const drafts = policies.filter(p => p.status === "DRAFT").length;
      const actives = policies.filter(p => p.status === "ACTIVE").length;
      const renewSoon = policies.filter(p => p.status === "RENEW_SOON").length;
      const expired = policies.filter(p => p.status === "EXPIRED").length;
      const needsAttention = renewSoon + expired;
      const carPolicies = policies.filter(p => p.type === "CAR").length;
      const motoPolicies = policies.filter(p => p.type === "MOTORCYCLE").length;

      return {
        total: policies.length,
        drafts,
        actives,
        renewSoon,
        expired,
        needsAttention,
        carPolicies,
        motoPolicies,
      };
    },
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    }
  );
}

// Hook for policy filters (client-side filtering logic)
export function usePolicyFilters() {
  const filterPolicies = (
    policies: PolicyData[],
    filters: {
      status?: string;
      assetType?: string;
      search?: string;
    }
  ) => {
    let filtered = [...policies];

    // Filter by status
    if (filters.status && filters.status !== "all") {
      if (filters.status === "attention") {
        filtered = filtered.filter(
          (p) => p.status === "RENEW_SOON" || p.status === "EXPIRED"
        );
      } else {
        filtered = filtered.filter((p) => p.status === filters.status);
      }
    }

    // Filter by asset type
    if (filters.assetType && filters.assetType !== "all") {
      filtered = filtered.filter((p) => p.type === filters.assetType);
    }

    // Filter by search term
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter((p) => {
        const policyNumber = p.policyNumber?.toLowerCase() || "";
        const productName = p.productName?.toLowerCase() || "";
        const brokerName = p.brokerName?.toLowerCase() || "";
        const brand = p.asset?.vehicleDetails?.brand?.toLowerCase() || "";
        const model = p.asset?.vehicleDetails?.model?.toLowerCase() || "";
        const insuredNames = p.insuredParties
          .map(ip => ip.fullName.toLowerCase())
          .join(" ");

        return (
          policyNumber.includes(searchTerm) ||
          productName.includes(searchTerm) ||
          brokerName.includes(searchTerm) ||
          brand.includes(searchTerm) ||
          model.includes(searchTerm) ||
          insuredNames.includes(searchTerm)
        );
      });
    }

    return filtered;
  };

  const getFilterCounts = (policies: PolicyData[]) => {
    const drafts = policies.filter(p => p.status === "DRAFT").length;
    const actives = policies.filter(p => p.status === "ACTIVE").length;
    const renewSoon = policies.filter(p => p.status === "RENEW_SOON").length;
    const expired = policies.filter(p => p.status === "EXPIRED").length;
    const needsAttention = renewSoon + expired;
    const carPolicies = policies.filter(p => p.type === "CAR").length;
    const motoPolicies = policies.filter(p => p.type === "MOTORCYCLE").length;

    return {
      total: policies.length,
      drafts,
      actives,
      renewSoon,
      expired,
      needsAttention,
      carPolicies,
      motoPolicies,
    };
  };

  return {
    filterPolicies,
    getFilterCounts,
  };
}