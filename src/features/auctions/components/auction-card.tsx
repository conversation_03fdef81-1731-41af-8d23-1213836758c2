import React from "react";
import { ZCard } from "@/components/shared/z-card";
import { cn } from "@/lib/utils";

interface AuctionCardProps {
  clientName: string;
  coverageType: string;
  timeRemaining?: string;
  price?: string | number;
  assetIcon?: React.ReactNode;
  className?: string;
}

export function AuctionCard({
  clientName,
  coverageType,
  timeRemaining,
  price,
  assetIcon,
  className,
  ...props
}: AuctionCardProps & Omit<React.HTMLAttributes<HTMLDivElement>, "title">) {
  return (
    <ZCard variant="auction" className={cn("", className)} {...props}>
      <div className="flex justify-between items-start">
        <div className="flex gap-3">
          {assetIcon && <div>{assetIcon}</div>}
          <div>
            <h3 className="font-medium">{clientName}</h3>
            <p className="text-sm text-muted-foreground">{coverageType}</p>
          </div>
        </div>
        <div className="text-right">
          {timeRemaining && (
            <div className="text-xs font-medium px-2 py-1 bg-orange-100 text-orange-800 rounded">
              {timeRemaining}
            </div>
          )}
          {price && (
            <div className="mt-2 font-bold">
              {typeof price === "number" ? `${price}€` : price}
            </div>
          )}
        </div>
      </div>
    </ZCard>
  );
}