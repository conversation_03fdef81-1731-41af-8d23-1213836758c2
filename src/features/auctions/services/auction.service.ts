import { db } from "@/lib/db";
import { AuctionState, type Auction, type Prisma } from "@prisma/client";

export const AuctionService = {
  // Create a new auction
  async create(data: Prisma.PolicyAuctionCreateInput): Promise<Auction> {
    return db.policyAuction.create({
      data,
      include: {
        user: true,
        policy: true,
        bids: {
          include: {
            broker: true
          }
        }
      }
    });
  },

  // Get an auction by ID
  async getById(id: string): Promise<Auction | null> {
    return db.policyAuction.findUnique({
      where: { id },
      include: {
        user: true,
        policy: true,
        bids: {
          include: {
            broker: true
          }
        }
      }
    });
  },

  // Get auctions by user ID
  async getByUserId(userId: string): Promise<Auction[]> {
    return db.policyAuction.findMany({
      where: { userId },
      include: {
        policy: true,
        bids: {
          include: {
            broker: true
          }
        }
      }
    });
  },

  // Get active auctions
  async getActive(): Promise<Auction[]> {
    const now = new Date();
    return db.policyAuction.findMany({
      where: {
        status: 'OPEN',
        endDate: {
          gt: now
        }
      },
      include: {
        user: true,
        policy: true,
        bids: {
          include: {
            broker: true
          }
        }
      }
    });
  },

  // Update an auction
  async update(id: string, data: Prisma.PolicyAuctionUpdateInput): Promise<Auction> {
    return db.policyAuction.update({
      where: { id },
      data,
      include: {
        user: true,
        policy: true,
        bids: {
          include: {
            broker: true
          }
        }
      }
    });
  },

  // Close an auction
  async close(id: string): Promise<Auction> {
    return db.policyAuction.update({
      where: { id },
      data: {
        status: 'CLOSED'
      },
      include: {
        bids: {
          include: {
            broker: true
          }
        }
      }
    });
  },

  // Delete an auction
  async delete(id: string): Promise<Auction> {
    return db.policyAuction.delete({
      where: { id }
    });
  },

  // List auctions with pagination and optional filters
  async list(params: {
    skip?: number;
    take?: number;
    where?: Prisma.PolicyAuctionWhereInput;
    orderBy?: Prisma.PolicyAuctionOrderByWithRelationInput;
  }): Promise<{ auctions: Auction[]; total: number }> {
    const { skip = 0, take = 10, where, orderBy } = params;
    
    const [auctions, total] = await Promise.all([
      db.policyAuction.findMany({
        skip,
        take,
        where,
        orderBy,
        include: {
          user: true,
          policy: true,
          bids: {
            include: {
              broker: true
            }
          }
        }
      }),
      db.policyAuction.count({ where })
    ]);

    return { auctions, total };
  },

  // Create an auction from a policy
  async createAuctionFromPolicy(policyId: string): Promise<Auction> {
    const policy = await db.policy.findUnique({
      where: { id: policyId },
      include: { user: true }
    });

    if (!policy || !policy.user) {
      throw new Error("Policy or user not found for the given policy ID.");
    }

    const endDate = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days from now

    const auctionData: Prisma.PolicyAuctionCreateInput = {
      user: { connect: { id: policy.userId } },
      policy: { connect: { id: policyId } },
      startDate: new Date(),
      endDate: endDate,
      workingHoursClosedAt: endDate, // Setting a default value
      status: AuctionState.OPEN,
    };

    return db.policyAuction.create({ data: auctionData });
  }
};

// Standalone function for closing expired auctions (used by cron job)
export async function closeExpiredAuctions(): Promise<{ count: number; closedAuctions: string[] }> {
  const now = new Date();
  
  // Find all open auctions that have expired
  const expiredAuctions = await db.policyAuction.findMany({
    where: {
      status: AuctionState.OPEN,
      endDate: {
        lt: now
      }
    },
    select: {
      id: true
    }
  });

  if (expiredAuctions.length === 0) {
    return { count: 0, closedAuctions: [] };
  }

  // Close all expired auctions
  await db.policyAuction.updateMany({
    where: {
      id: {
        in: expiredAuctions.map(auction => auction.id)
      }
    },
    data: {
      status: AuctionState.CLOSED
    }
  });

  const closedAuctionIds = expiredAuctions.map(auction => auction.id);
  
  return {
    count: closedAuctionIds.length,
    closedAuctions: closedAuctionIds
  };
}