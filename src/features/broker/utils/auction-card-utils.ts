// Shared typography constants for consistent design
export const AUCTION_CARD_TYPOGRAPHY = {
  policyNumber: "font-semibold text-base text-gray-900 leading-tight tracking-tight",
  timerBadge: "bg-gray-100 px-2 py-1 rounded-md text-xs text-gray-600 font-medium",
  assetType: "text-xs text-muted-foreground font-medium",
  coverageType: "text-xs text-muted-foreground font-medium",
  premium: "text-xs text-muted-foreground font-medium",
  premiumSuffix: "text-xs text-muted-foreground font-medium",
  clientName: "text-xs text-muted-foreground font-medium",
  bidAmount: "font-bold text-base"
} as const;

// Shared asset type text logic
export const getAssetTypeText = (assetType: string) => {
  switch (assetType) {
    case "CAR":
      return "Seguro de coche";
    case "MOTORCYCLE":
      return "Seguro de moto";
    default:
      return "Seguro de vehículo";
  }
};

// Shared coverage type display logic
export const getCoverageTypeDisplay = (coverageType: string) => {
  switch (coverageType) {
    case "COMPREHENSIVE":
      return "Seguro Todo Riesgo";
    case "THIRD_PARTY":
      return "Seguro a Terceros";
    case "THIRD_PARTY_EXTENDED":
      return "Seguro a Terceros Ampliado";
    case "COMPREHENSIVE_WITH_DEDUCTIBLE":
      return "Seguro Todo Riesgo con Franquicia";
    default:
      return "Seguro Todo Riesgo";
  }
};

// Shared client name masking logic
export const maskClientName = (name: string) => {
  const parts = name.split(' ');
  if (parts.length === 0) {
    return "****";
  }
  // Always show only the first name followed by asterisks
  return `${parts[0]} ****`;
};

// Shared premium formatting logic
export const formatPremium = (amount: number) => `${amount}€`;
export const formatPremiumWithSuffix = (amount: number) => `${amount}€/Anual`;