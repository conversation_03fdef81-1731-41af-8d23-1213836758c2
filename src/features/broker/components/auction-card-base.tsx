"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Clock, Users } from "lucide-react";
import { cn } from "@/lib/utils";
import { BaseAuction } from "../types/auction";

interface AuctionCardBaseProps {
  auction: BaseAuction;
  children?: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

export function AuctionCardBase({ auction, children, className, onClick }: AuctionCardBaseProps) {
  // DEBUG: Log auction timer data
  console.log("AuctionCardBase - Auction ID:", auction.id);
  console.log("AuctionCardBase - Time Remaining:", auction.timeRemaining);
  console.log("AuctionCardBase - Auction End Time:", (auction as any).auctionEndTime);
  console.log("AuctionCardBase - Current Time:", new Date());

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case "high": return "bg-red-100 text-red-800";
      case "medium": return "bg-yellow-100 text-yellow-800";
      case "low": return "bg-green-100 text-green-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getUrgencyLabel = (urgency: string) => {
    switch (urgency) {
      case "high": return "Alta";
      case "medium": return "Media";
      case "low": return "Baja";
      case "completed": return "Finalizada";
      default: return "Normal";
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-ES', { style: 'currency', currency: 'EUR' }).format(amount);
  };

  const getCoverageTypeLabel = (type: string) => {
    switch (type) {
      case "COMPREHENSIVE": return "Cobertura Completa";
      case "THIRD_PARTY": return "Terceros";
      case "THIRD_PARTY_EXTENDED": return "Terceros Ampliado";
      case "COMPREHENSIVE_WITH_DEDUCTIBLE": return "Cobertura Completa con Franquicia";
      default: return type.replace(/_/g, ' ');
    }
  };

  return (
    <Card 
      className={cn("mb-4 hover:shadow-md transition-shadow", className, onClick && "cursor-pointer")}
      onClick={onClick}
    >
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-sm font-medium">{auction.clientName}</CardTitle>
            <p className="text-xs text-muted-foreground">{auction.policyNumber}</p>
          </div>
          <Badge className={cn("text-xs", getUrgencyColor(auction.urgency))}>
            {getUrgencyLabel(auction.urgency)}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Vehicle/Asset Info */}
        <div className="flex items-center gap-2 text-sm">
          <Badge variant="outline" className="text-xs">
            {auction.assetType === "CAR" ? "🚗" : "🏍️"}
          </Badge>
          <span className="text-muted-foreground">
            {auction.brand} {auction.model} ({auction.year})
          </span>
        </div>

        {/* Coverage & Location */}
        <div className="text-sm text-muted-foreground space-y-1">
          <div>📍 {auction.location}</div>
          <div>🛡️ {getCoverageTypeLabel(auction.coverageType)}</div>
        </div>

        {/* Time & Participants */}
        <div className="flex justify-between items-center text-sm">
          <div className="flex items-center gap-1 text-muted-foreground">
            <Clock className="h-3 w-3" />
            <span>{auction.timeRemaining}</span>
          </div>
          <div className="flex items-center gap-1 text-muted-foreground">
            <Users className="h-3 w-3" />
            <span>{auction.participantCount}</span>
          </div>
        </div>

        {/* Current Premium */}
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium">Prima:</span>
          <span className="text-sm font-bold">{formatCurrency(auction.currentPremium)}</span>
        </div>

        {/* Column-specific content */}
        {children}
      </CardContent>
    </Card>
  );
}