"use client";

import React, { useState } from "react";
import { ZCard } from "@/components/shared/z-card";
import { PrimaryButton } from "@/components/shared/primary-button";
import { PolicyDetailsDrawer } from "@/components/shared/PolicyDetailsDrawer";
import { cn } from "@/lib/utils";
import { PolicyStatus, AssetType } from "@prisma/client";
import { PolicyData } from "@/features/account-holder/hooks/usePolicies";
import { Eye, RotateCcw } from "lucide-react";
import Link from "next/link";
import { formatDate, formatCurrency, getAssetTypeIcon } from "@/lib/utils";
import { formatAssetType } from "@/lib/format-asset-type";
import {
  getSubtitleByStatus,
  isExpiredStatus,
  isRenewSoonStatus,
  isDraftStatus,
  isRejectedStatus
} from "@/features/policies/utils/policy-status";
import { PolicyStatusBadge } from "@/features/policies/components/policy-status-badge";
import {
  translateFuelType,
  translateUsageType,
  translateGarageType,
  translateKmRange
} from "@/features/policies/utils/translations";

interface PolicyCardProps {
  policy: PolicyData;
  id: string;
  policyNumber: string;
  assetInfo: string;
  insurerName: string;
  productName: string;
  premium: number;
  status: PolicyStatus;
  endDate: Date | string | null;
  insuredName: string;
  coverageCount: number;
  assetType: AssetType;
  className?: string;
  baseUrl?: string; // Allow different base URLs for different roles
}

export function PolicyCard({
  policy,
  id,
  policyNumber,
  assetInfo,
  insurerName,
  productName,
  premium,
  status,
  endDate,
  insuredName,
  coverageCount,
  assetType,
  className,
  baseUrl = "/account-holder", // Default to account-holder, can be overridden
  ...props
}: PolicyCardProps & Omit<React.HTMLAttributes<HTMLDivElement>, "id">) {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  // Transform policy data for the drawer
  const transformedPolicyData = {
    id: policy.id,
    policyNumber: policy.policyNumber || "",
    status: policy.status as any,
    insurer: policy.insurerCompany || "",
    product: policy.productName || "",
    policyType: policy.asset?.assetType ? formatAssetType(policy.asset.assetType) : "",
    validity: policy.endDate ? `Hasta ${formatDate(policy.endDate)}` : 'Fecha de vencimiento no disponible',
    annualPremium: formatCurrency(policy.premium || 0),
    holderName: insuredName || "",
    birthDate: "", // This data is not available in the policy object
    gender: "", // This data is not available in the policy object
    phone: "", // This data is not available in the policy object
    email: "", // This data is not available in the policy object
    // Vehicle data mapping from database structure with proper type conversion and translation
    vehiclePlate: policy.asset?.vehicleDetails?.licensePlate || "",
    vehicleFirstRegistrationDate: policy.asset?.vehicleDetails?.firstRegistrationDate
      ? (() => {
          try {
            const date = policy.asset.vehicleDetails.firstRegistrationDate instanceof Date
              ? policy.asset.vehicleDetails.firstRegistrationDate
              : new Date(policy.asset.vehicleDetails.firstRegistrationDate);
            return isNaN(date.getTime()) ? "" : date.toLocaleDateString('es-ES');
          } catch {
            return "";
          }
        })()
      : "",
    vehicleBrand: policy.asset?.vehicleDetails?.brand || "",
    vehicleModel: policy.asset?.vehicleDetails?.model || "",
    vehicleVersion: policy.asset?.vehicleDetails?.version || "",
    vehicleManufacturingYear: policy.asset?.vehicleDetails?.year?.toString() || "",
    vehicleType: policy.asset?.assetType ? formatAssetType(policy.asset.assetType) : "",
    vehicleFuelType: policy.asset?.vehicleDetails?.fuelType
      ? translateFuelType(policy.asset.vehicleDetails.fuelType)
      : "",
    vehicleVin: policy.asset?.vehicleDetails?.chassisNumber || "",
    vehiclePower: policy.asset?.vehicleDetails?.powerCv?.toString() || "",
    vehicleSeats: policy.asset?.vehicleDetails?.seats?.toString() || "",
    vehicleUsageType: policy.asset?.vehicleDetails?.usageType
      ? translateUsageType(policy.asset.vehicleDetails.usageType)
      : "",
    vehicleGarageType: policy.asset?.vehicleDetails?.garageType
      ? translateGarageType(policy.asset.vehicleDetails.garageType)
      : "",
    vehicleKmPerYear: policy.asset?.vehicleDetails?.kmPerYear
      ? translateKmRange(policy.asset.vehicleDetails.kmPerYear)
      : "",
    vehicleIsLeased: policy.asset?.vehicleDetails?.isLeased === true ? "Sí" : "No",
    coverages: policy.coverages ? policy.coverages.map(c => ({ title: c.customName || c.type, limit: c.limit, description: c.description || '', guaranteeType: c.type, severity: 'Medium' })) : [],
    document: policy.document,
  };
  const isExpired = isExpiredStatus(status);
  const isRenewSoon = isRenewSoonStatus(status);
  const isDraft = isDraftStatus(status);
  const isRejected = isRejectedStatus(status);

  return (
    <ZCard
      variant="policy"
      className={cn(
        "transition-all hover:shadow-lg p-6",
        className
      )}
      {...props}
    >
      <div className="space-y-4">
        {/* Header with Asset Icon, Title and Status Badge */}
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-4">
            <div className="flex-shrink-0 w-12 h-12 rounded-full bg-gradient-to-br from-primary/10 to-primary/20 border border-primary/20 flex items-center justify-center text-xl shadow-sm">
              {getAssetTypeIcon(assetType)}
            </div>
            <div>
              <h3 className="font-bold text-xl text-foreground mb-1">
                {isDraft ? "Verificación Pendiente" : isRejected ? "Rechazado" : assetInfo}
              </h3>
              {!isDraft && !isRejected && (
                <div className="text-sm text-muted-foreground">
                  {getSubtitleByStatus(status, endDate || undefined)}
                </div>
              )}
            </div>
          </div>
          
          {/* Status Badge */}
          <div>
            <PolicyStatusBadge status={status} />
          </div>
        </div>

        {/* Main Content - Conditional Layout Based on Status */}
        {isDraft || isRejected ? (
          /* DRAFT or REJECTED Status - Special Layout */
          <div className="space-y-3">
            <div>
              <p className="text-sm text-muted-foreground leading-relaxed">
                {isDraft 
                  ? "Tu póliza está pasando por nuestro proceso de verificación. Tan pronto como la validación esté completa tendrás acceso completo a sus datos."
                  : "El documento subido no es una póliza válida o no se puede leer claramente. Por favor, sube una copia correcta y legible para continuar."
                }
              </p>
            </div>
          </div>
        ) : (
          /* Normal Status - Two Column Layout */
          <div className="grid grid-cols-2 gap-8">
            {/* Left Column - PÓLIZA */}
            <div>
              <div className="text-xs font-semibold text-muted-foreground uppercase tracking-wide mb-3">
                PÓLIZA
              </div>
              <div className="space-y-2">
                <div className="font-semibold text-foreground">
                  {insurerName}
                </div>
                <div className="text-sm text-muted-foreground">
                  {productName}
                </div>
                <div className="font-medium text-foreground">
                  {insuredName}
                </div>
                <div className="text-sm text-muted-foreground">
                  {policyNumber}
                </div>
              </div>
            </div>

            {/* Right Column - COBERTURA */}
            <div>
              <div className="text-xs font-semibold text-muted-foreground uppercase tracking-wide mb-3">
                COBERTURA
              </div>
              <div className="space-y-2">
                <div>
                  <span className="font-semibold text-foreground">Prima: </span>
                  <span className="text-foreground">
                    {premium && premium > 0 ? `${formatCurrency(premium)}/año` : "Sin prima anual"}
                  </span>
                </div>
                <div className="text-sm text-muted-foreground">
                  Cantidad de coberturas: {coverageCount > 0 ? coverageCount : "No disponible"}
                </div>
                <div>
                  <span className="font-semibold text-foreground">Vence: </span>
                  <span className={cn(
                    "text-foreground",
                    isExpired && "text-red-600",
                    isRenewSoon && "text-orange-600"
                  )}>
                    {endDate ? formatDate(endDate) : "Fecha de vencimiento no disponible"}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end items-center pt-2">
          {/* Action Button */}
          <div>
            {(isDraft || isRejected) && (
              <PrimaryButton 
                size="sm" 
                className="h-8 text-xs opacity-50 cursor-not-allowed"
                disabled
              >
                <Eye className="h-4 w-4 mr-2" />
                Ver detalles
              </PrimaryButton>
            )}
            
            {(status === PolicyStatus.ACTIVE) && (
              <>
                <PrimaryButton 
                  size="sm" 
                  className="h-8 text-xs"
                  onClick={() => setIsDrawerOpen(true)}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Ver detalles
                </PrimaryButton>
                <PolicyDetailsDrawer
                   isOpen={isDrawerOpen}
                   onClose={() => setIsDrawerOpen(false)}
                   mode="account-holder"
                   policyData={transformedPolicyData}
                 />
              </>
            )}
            
            {isRenewSoon && (
              <Link href={`${baseUrl}/policies/${id}/renew`}>
                <PrimaryButton size="sm" className="h-8 text-xs">
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Renovar
                </PrimaryButton>
              </Link>
            )}
            
            {isExpired && (
              <>
                <PrimaryButton 
                  size="sm" 
                  className="h-8 text-xs"
                  onClick={() => setIsDrawerOpen(true)}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Ver detalles
                </PrimaryButton>
                <PolicyDetailsDrawer
                   isOpen={isDrawerOpen}
                   onClose={() => setIsDrawerOpen(false)}
                   mode="account-holder"
                   policyData={transformedPolicyData}
                 />
              </>
            )}
          </div>
        </div>
      </div>
    </ZCard>
  );
}