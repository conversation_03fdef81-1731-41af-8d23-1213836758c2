import { Badge } from "@/components/ui/badge";
import { PolicyStatus } from "@prisma/client";
import { getStatusDisplayName, isExpiredStatus, isRenewSoonStatus, isDraftStatus, isRejectedStatus } from "@/features/policies/utils/policy-status";
import { cn } from "@/lib/utils";

interface PolicyStatusBadgeProps {
  status: PolicyStatus;
  className?: string;
}

export const PolicyStatusBadge = ({ status, className }: PolicyStatusBadgeProps) => {
  const getStatusVariant = (status: PolicyStatus) => {
    if (isExpiredStatus(status)) return "destructive";
    if (isRejectedStatus(status)) return "destructive";
    if (isDraftStatus(status)) return "secondary";
    return "default"; // ACTIVE
  };

  const getStatusClassName = (status: PolicyStatus) => {
    if (isRenewSoonStatus(status)) {
      return "border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200";
    }
    if (isDraftStatus(status)) {
      return "border-transparent bg-gray-100 text-gray-800 hover:bg-gray-200";
    }
    if (isRejectedStatus(status)) {
      return "border-transparent bg-red-100 text-red-800 hover:bg-red-200";
    }
    return "";
  };

  return (
    <Badge
      variant={getStatusVariant(status)}
      className={cn(getStatusClassName(status), className)}
    >
      {getStatusDisplayName(status)}
    </Badge>
  );
};