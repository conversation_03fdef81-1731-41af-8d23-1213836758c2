# 5. Component Organization

## 5.1 Project Structure

```
zee-next-app/
├── docs/
│   ├── architecture/
│   │   ├── 1-introduction.md
│   │   ├── 2-enhancement-scope.md
│   │   ├── 3-tech-stack-alignment.md
│   │   ├── 4-data-models.md
│   │   ├── 5-component-organization.md
│   │   ├── 6-api-design-and-integration.md
│   │   ├── 7-security-and-compliance.md
│   │   ├── 8-testing-strategy.md
│   │   ├── 9-deployment-and-infrastructure.md
│   │   ├── 10-coding-standards-and-quality.md
│   │   ├── 11-development-workflow.md
│   │   ├── 12-future-evolution.md
│   │   └── index.md
│   └── plans/
│       └── account-holder-journey-enhancement-prd.md
├── public/
│   ├── assets/
│   └── favicon.ico
├── src/
│   ├── app/
│   │   ├── (account-holder)/
│   │   │   ├── dashboard/
│   │   │   │   └── page.tsx
│   │   │   ├── layout.tsx
│   │   │   └── policies/
│   │   │       ├── [policy_id]/
│   │   │       │   └── page.tsx
│   │   │       └── page.tsx
│   │   ├── (admin)/
│   │   │   ├── dashboard/
│   │   │   │   └── page.tsx
│   │   │   └── layout.tsx
│   │   ├── (auth)/
│   │   │   ├── login/
│   │   │   │   └── page.tsx
│   │   │   └── layout.tsx
│   │   ├── (broker)/
│   │   │   ├── dashboard/
│   │   │   │   └── page.tsx
│   │   │   └── layout.tsx
│   │   ├── api/
│   │   │   ├── account-holder/
│   │   │   │   ├── policies/
│   │   │   │   │   ├── [policy_id]/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   └── route.ts
│   │   │   │   └── route.ts
│   │   │   ├── admin/
│   │   │   │   └── route.ts
│   │   │   ├── auth/
│   │   │   │   └── route.ts
│   │   │   ├── broker/
│   │   │   │   └── route.ts
│   │   │   └── route.ts
│   │   ├── layout.tsx
│   │   └── page.tsx
│   ├── components/
│   │   ├── ui/
│   │   │   ├── button.tsx
│   │   │   └── ... (Shadcn UI components)
│   │   └── shared/
│   │       ├── Header.tsx
│   │       └── Footer.tsx
│   ├── lib/
│   │   ├── auth.ts
│   │   ├── db.ts
│   │   ├── utils.ts
│   │   └── ... (infrastructure utilities)
│   ├── styles/
│   │   └── globals.css
│   └── types/
│       └── index.d.ts
├── .env.example
├── .eslintrc.json
├── .gitignore
├── next.config.mjs
├── package.json
├── pnpm-lock.yaml
├── postcss.config.js
├── prisma/
│   ├── schema.prisma
│   └── seed.ts
├── README.md
├── tailwind.config.ts
├── tsconfig.json
└── vercel.json
```

## 5.2 Component Enhancement Patterns

- **UI/UX Standards:**
  - **Language Conventions:** All UI text is in Spanish. Code comments, variable names, and technical documentation are in English.
  - **Color Palette:** Utilizes a predefined color palette for consistency.
  - **Typography:** Adheres to established font families and sizing.
- **API Design and Integration:**
  - **Current Architecture:** All database operations are exclusively handled via server-side API routes. Client-side `createClient()` is strictly prohibited for database access.
  - **Security Requirements:** Mandatory server-side security for all database operations. Client-side usage of Supabase `createClient()` is limited to authentication purposes only.
  - **API Route Structure:** API routes are organized by business domain and role (e.g., `src/app/api/account-holder/policies`).
  - **Integration Patterns:**
    - **Google Gemini API:** Integrated for document processing and AI-driven features.
    - **Brevo SMTP:** Used for transactional email notifications.
    - **Supabase:** Provides PostgreSQL database, authentication, and real-time capabilities.
    - **Cloudflare R2:** Utilized for secure object storage.
- **External API Integration Guidelines:**
  - All external API calls must be encapsulated within server-side functions.
  - API keys and sensitive credentials must be stored securely (e.g., environment variables) and never exposed client-side.
  - Robust error handling and retry mechanisms should be implemented for external API calls.
  - Rate limiting and usage quotas for external APIs must be respected.