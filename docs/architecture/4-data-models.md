# 4. Data Models

## 4.1 Entity Relationship Diagram (ERD)

```mermaid
ERD
    USER ||--o{ POLICY : has
    POLICY ||--o{ CLAIM : has
    POLICY ||--o{ PAYMENT : has
    USER {string id, string email, string role}
    POLICY {string id, string user_id, string policy_number, string type, date start_date, date end_date, string status}
    CLAIM {string id, string policy_id, date claim_date, string description, string status}
    PAYMENT {string id, string policy_id, date payment_date, float amount, string status}
```

## 4.2 Key Data Entities

- **User:** Represents an individual interacting with the system. Includes `id`, `email`, and `role` (e.g., `ADMIN`, `BROKER`, `ACCOUNT_HOLDER`).
- **Policy:** Core entity representing an insurance policy. Attributes include `id`, `user_id` (foreign key to User), `policy_number`, `type`, `start_date`, `end_date`, and `status` (e.g., `ACTIVE`, `EXPIRED`, `PENDING`).
- **Claim:** Represents a claim made against a policy. Includes `id`, `policy_id` (foreign key to Policy), `claim_date`, `description`, and `status`.
- **Payment:** Records payments related to policies. Includes `id`, `policy_id` (foreign key to Policy), `payment_date`, `amount`, and `status`.

## 4.3 Data Flow

1. **User Authentication:** Users authenticate via Supabase Auth, and their `role` determines access to specific application routes and data.
2. **Policy Management:** Account Holders can view and manage their policies. Brokers can manage policies for their clients. Admins have full oversight.
3. **Claim Processing:** Users submit claims, which are then processed and updated by internal roles.
4. **Payment Handling:** Payments are recorded and associated with policies.
5. **Data Storage:** All data is persisted in PostgreSQL via Supabase, accessed exclusively through server-side API routes.