# 10. Coding Standards and Conventions

## 10.1 Established Standards

**Architectural Standards:**
- Screaming Architecture compliance 
- Role-based organization strictly enforced
- DRY principle implementation across codebase
- Clear separation of concerns

**Security Standards:**
- Server-side API routes for all data operations
- No client-side database access
- Proper authentication validation
- Zod schema validation for all inputs

## 10.2 Core Standards

- **Languages & Runtimes:** TypeScript 5.3.3, Node.js 20.11.0
- **Style & Linting:** ESLint with Next.js config, TypeScript strict mode
- **Test Organization:** Tests co-located with source files using `.test.ts` suffix

## 10.3 Critical Rules

- **Server-Side Database Access Only:** NEVER use client-side Supabase for database operations. All CRUD operations must use server-side API routes to prevent anonymous API key exposure.
- **Spanish UI Text:** All user-facing text (labels, buttons, titles, tooltips, placeholders, error messages) must be written in Spanish.
- **English Code:** All code (variable names, function names, class names, components, database models, schema, file names, comments) must be written in English.
- **Role-Based Organization:** Place domain-specific components, hooks, or services in `src/features/{domainName}/` and role-specific UI pages in `src/app/{role}/{domain}/`.
- **Single Prisma Client:** Always import the singleton Prisma client from `src/lib/db.ts`. Never create new instances.
- **TypeScript Compliance:** All code must compile without TypeScript errors. Do not rely on `ignoreBuildErrors` flag.
- **Security First:** Never expose sensitive data in logs, error messages, or client-side code. All authentication must be validated server-side.

## 10.4 Language and Naming Conventions

**Code Layer (English):**
- Variable names: `camelCase`
- Function names: `camelCase`
- Class names: `PascalCase`
- Component names: `PascalCase`
- File names: `kebab-case` or `camelCase`
- Database models: `PascalCase`

**UI Layer (Spanish):**
- All user-visible text in professional Spanish
- Consistent terminology across platform
- Error messages and validation feedback
- Button labels and form placeholders

## 10.5 Code Quality Requirements

**TypeScript Standards:**
- Zero build errors tolerance
- Proper type definitions for all functions
- Interface definitions for complex objects
- Generic type usage where appropriate

**Component Standards:**
- Single responsibility principle
- Proper prop typing with TypeScript
- Consistent error handling patterns
- Accessibility considerations

## 10.6 Color Palette

Use only these colors for consistent brand identity:
- White (`#FFFFFF`)
- Black (`#000000`) 
- Lime Green (`#3AE386`)
- Emerald Green (`#3EA050`)

## 10.7 Architectural Boundaries

- **Domain-specific components, hooks, or services:** `src/features/{domainName}/`
- **Role-specific UI pages:** `src/app/{role}/{domain}/`
- **Generic shadcn/ui components:** `src/components/ui/`
- **Shared application components:** `src/components/shared/`
- **Infrastructure components:** `src/lib/`

## 10.8 Security Requirements

- **API Routes Only:** All database operations must go through `/api/` endpoints
- **Server-Side Validation:** Use Zod schemas to validate all incoming data
- **Authentication:** Validate user sessions server-side before database operations
- **File Uploads:** Never use client-side Supabase for file operations
- **Error Handling:** Never expose internal errors to client

---
