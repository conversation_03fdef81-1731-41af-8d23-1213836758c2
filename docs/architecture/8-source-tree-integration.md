# 8. Source Tree Integration

## 8.1 Project Structure Overview

```
zee-next-app/
├── .bmad-core/                     # BMAD system configuration
│   ├── core-config.yaml           # Core BMAD configuration
│   ├── tasks/                     # BMAD task definitions
│   ├── templates/                 # Document templates
│   └── workflows/                 # BMAD workflows
├── .next/                         # Next.js build output (generated)
├── docs/                          # Project documentation
│   ├── architecture/              # Architecture documentation
│   │   ├── 10-coding-standards-and-conventions.md    # Development standards
│   │   ├── 3-tech-stack-alignment.md         # Technology stack details
│   │   └── 8-source-tree-integration.md        # This file
│   ├── changelog/                 # Version history
│   ├── plans/                     # Development plans
│   ├── stories/                   # User stories (BMAD)
│   ├── architecture.md           # Main architecture document
│   └── prd.md                     # Product Requirements Document
├── prisma/                        # Database schema and migrations
│   ├── schema.prisma             # Database schema definition
│   └── seed.ts                   # Database seeding script
├── public/                        # Static assets
│   ├── images/                   # Image assets
│   └── icons/                    # Icon assets
├── src/                          # Source code (Screaming Architecture)
│   ├── app/                      # Next.js App Router (Role-based organization)
│   │   ├── (public)/             # Public routes (landing, auth)
│   │   │   ├── auth/             # Authentication pages
│   │   │   ├── landing/          # Landing page
│   │   │   └── layout.tsx        # Public layout
│   │   ├── account-holder/       # Account holder role routes
│   │   │   ├── dashboard/        # Account holder dashboard
│   │   │   ├── policies/         # Policy management
│   │   │   ├── auctions/         # Auction creation/management
│   │   │   ├── settings/         # Account settings
│   │   │   ├── support/          # Support pages
│   │   │   └── layout.tsx        # Account holder layout
│   │   ├── admin/                # Admin role routes
│   │   │   ├── dashboard/        # Admin dashboard
│   │   │   ├── policies/         # Policy oversight
│   │   │   ├── auctions/         # Auction management
│   │   │   ├── settings/         # System settings
│   │   │   ├── support/          # Support management
│   │   │   └── layout.tsx        # Admin layout
│   │   ├── broker/               # Broker role routes
│   │   │   ├── dashboard/        # Broker dashboard
│   │   │   ├── crm/              # Customer relationship management
│   │   │   ├── portfolio/        # Portfolio management
│   │   │   ├── policies/         # Policy views
│   │   │   ├── auctions/         # Auction participation
│   │   │   ├── settings/         # Broker settings
│   │   │   ├── support/          # Support access
│   │   │   └── layout.tsx        # Broker layout
│   │   ├── api/                  # API routes (server-side only)
│   │   │   ├── auth/             # Authentication endpoints
│   │   │   ├── policies/         # Policy CRUD operations
│   │   │   ├── auctions/         # Auction operations
│   │   │   ├── users/            # User management
│   │   │   └── upload/           # File upload endpoints
│   │   ├── _components/          # App-level shared components
│   │   ├── globals.css           # Global styles
│   │   ├── layout.tsx            # Root layout
│   │   └── page.tsx              # Root page
│   ├── components/               # Reusable UI components
│   │   ├── ui/                   # Generic shadcn/ui components
│   │   │   ├── button.tsx        # Button component
│   │   │   ├── card.tsx          # Card component
│   │   │   ├── form.tsx          # Form components
│   │   │   └── ...               # Other UI primitives
│   │   └── shared/               # Shared application components
│   │       ├── nav-main.tsx      # Main navigation
│   │       ├── sidebar.tsx       # Sidebar component
│   │       └── ...               # Other shared components
│   ├── features/                 # Business domain organization
│   │   ├── account-holder/       # Account holder specific features
│   │   │   ├── components/       # Account holder components
│   │   │   ├── hooks/            # Account holder hooks
│   │   │   ├── services/         # Account holder services
│   │   │   └── types/            # Account holder types
│   │   ├── admin/                # Admin specific features
│   │   │   ├── components/       # Admin components
│   │   │   ├── hooks/            # Admin hooks
│   │   │   ├── services/         # Admin services
│   │   │   └── types/            # Admin types
│   │   ├── auctions/             # Auction domain logic
│   │   │   ├── actions/          # Server actions
│   │   │   ├── components/       # Auction components
│   │   │   ├── hooks/            # Auction hooks
│   │   │   ├── services/         # Auction services
│   │   │   └── types/            # Auction types
│   │   ├── auth/                 # Authentication domain
│   │   │   ├── components/       # Auth components
│   │   │   ├── hooks/            # Auth hooks
│   │   │   ├── services/         # Auth services
│   │   │   └── types/            # Auth types
│   │   ├── broker/               # Broker specific features
│   │   │   ├── components/       # Broker components
│   │   │   ├── hooks/            # Broker hooks
│   │   │   ├── services/         # Broker services
│   │   │   └── types/            # Broker types
│   │   ├── policies/             # Policy domain logic
│   │   │   ├── actions/          # Server actions
│   │   │   ├── components/       # Policy components
│   │   │   ├── hooks/            # Policy hooks
│   │   │   ├── services/         # Policy services
│   │   │   └── types/            # Policy types
│   │   └── settings/             # Settings domain logic
│   │       ├── components/       # Settings components
│   │       ├── hooks/            # Settings hooks
│   │       ├── services/         # Settings services
│   │       └── types/            # Settings types
│   ├── lib/                      # Pure infrastructure layer
│   │   ├── supabase/             # Supabase configuration
│   │   │   ├── client.ts         # Client-side Supabase (auth only)
│   │   │   ├── server.ts         # Server-side Supabase
│   │   ├── zod/                  # Generated Zod schemas (from Prisma)
│   │   ├── db.ts                 # Database client singleton
│   │   ├── utils.ts              # Generic utilities only
│   │   └── validations.ts        # Shared validation schemas
│   └── types/                    # Global TypeScript types
│       ├── auth.ts               # Authentication types
│   │   ├── database.ts           # Database types
│   │   └── global.ts             # Global type definitions
├── .ai/                          # AI development logs
│   └── debug-log.md              # Debug information
├── .env.local                    # Environment variables (local)
├── .env.example                  # Environment variables template
├── .eslintrc.json                # ESLint configuration
├── .gitignore                    # Git ignore rules
├── next.config.js                # Next.js configuration
├── package.json                  # Dependencies and scripts
├── package-lock.json             # Dependency lock file
├── README.md                     # Project documentation
├── tailwind.config.ts            # Tailwind CSS configuration
└── tsconfig.json                 # TypeScript configuration
```

## 8.2 Architectural Principles

**✅ SCREAMING ARCHITECTURE COMPLIANCE:**

**Role-Based Organization:**
- All app routes organized by user roles (`admin/`, `broker/`, `account-holder/`)
- Business domains within roles clearly visible
- No technical domain groupings permitted

**Feature Domain Organization:**
- Domain-specific logic in `src/features/{domain}/`
- Shared components and utilities properly categorized
- Clear separation between infrastructure and business logic

**Security-First Structure:**
- **API Routes:** All database operations go through `/api/` endpoints
- **Server-Side Logic:** Business logic in server actions and API routes
- **Client-Side:** Only UI components and authentication

**Domain-Driven Design:**
- **Features Directory:** Each business domain has its own folder
- **Clear Boundaries:** Components, hooks, services, and types organized by domain
- **Shared Resources:** Common utilities in `lib/` and shared components in `components/shared/`

## 8.3 Integration Guidelines for New Features

**Pre-Development Checklist:**
1. **Acknowledge Current Architecture:** Confirm understanding of 100% Screaming Architecture compliance
2. **Identify Domain AND Role:** State both business domain and target user role
3. **Locate Relevant Files:** Explore current structure using established patterns
4. **Consult Schema:** Review `prisma/schema.prisma` for data model understanding
5. **Declare Intent:** Formulate clear plan maintaining role-based organization

**File Placement Rules:**
- **Role-specific pages:** `src/app/{role}/{domain}/`
- **Domain features:** `src/features/{domain}/`
- **Shared components:** `src/components/shared/`
- **Infrastructure:** `src/lib/`

## 8.4 Code Quality Standards

**TypeScript Compliance:**
- Maintain A+ compliance (zero build errors)
- No dependency on `ignoreBuildErrors` flag
- Follow established type patterns

**Code Conventions:**
- English for all code (variables, functions, classes, files, comments)
- Spanish for all UI text (labels, buttons, messages, placeholders)
- Consistent naming conventions (camelCase/PascalCase)

---
