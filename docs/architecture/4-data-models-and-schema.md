# 4. Data Models and Schema

## 4.1 Core Entity Relationships

```mermaid
erDiagram
    USER ||--o{ ACCOUNT_HOLDER_PROFILE : "has"
    USER ||--o{ BROKER_PROFILE : "has"
    USER ||--|{ POLICY : "manages"
    USER ||--|{ AUCTION : "creates"

    ACCOUNT_HOLDER_PROFILE ||--|{ POLICY : "owns"
    BROKER_PROFILE ||--|{ POLICY : "manages"
    BROKER_PROFILE ||--|{ BID : "places"

    POLICY ||--|{ VEHICLE : "insures"
    POLICY ||--o{ INSURED_PARTY : "covers"
    POLICY ||--o{ COVERAGE : "includes"
    POLICY ||--o{ POLICY_AUCTION : "has"

    POLICY_AUCTION ||--|{ BID : "receives"
```

## 4.2 Key Data Models

**User Management:**
- `User`: Authentication and role information (`CUSTOMER`, `<PERSON><PERSON>ER`, `ADMIN`)
- `CustomerProfile` / `BrokerProfile`: Role-specific profile data
- Role-based access patterns with Supabase RLS

**Policy Domain:**
- `Policy`: Complete insurance policy details
- `Asset`: Insured asset information (vehicles, properties)
- `InsuredParty`: Individuals covered by policies
- `Coverage`: Specific guarantees and financial details

**Auction Domain:**
- `Auction`: Insurance auction management
- `AuctionBid`: Broker bids on auctions
- Business hours validation and auction lifecycle management

## 4.3 Schema Enhancement Guidelines

**For New Models:**
- All models must include descriptive comments
- Follow established naming conventions (English, camelCase/PascalCase)
- Implement appropriate relationships and constraints
- Consider RLS policies for data security
- Validate against existing domain boundaries

---
