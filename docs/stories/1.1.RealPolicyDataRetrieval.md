# Story 1.1: RealPolicyDataRetrieval

## Status
Ready for Review

## Story
**As a** Account Holder,
**I want** see my real policies list on the “Mis Pólizas” page retrieved securely from the backend,
**so that** I can trust the information and manage my policies based on real data

## Acceptance Criteria
1. Policies list page loads policies via secure server‑side call (only user’s policies).
2. Empty‑state message shows if no policies exist.
3. Status filter shows correct subset when selected.
4. Search by policy number returns matching policy.
5. Row‑level security ensures a user cannot access another user’s policies.
6. API responds in <400 ms for ≤50 policies.

## Tasks / Subtasks
- [x] Implement `/api/account-holder/policies/list` Next.js route (AC:1,5)
  - [x] Query policies via Prisma including new relations
  - [x] Apply Supabase JWT to enforce RLS
- [x] Paginate server response (AC:6)
  - [x] Add limit/offset parameters
  - [x] Update UI to fetch next page on scroll
- [x] Refactor `PolicyList` component to consume server data (AC:1,3,4)
  - [x] Replace mock data hook
  - [x] Maintain filter & search state
  - [x] Happy path returns own policies
  - [x] Unauthorized path returns 403
  - [x] User with zero policies scenario
  - [x] Filter/search scenarios

## Dev Notes
- Use existing Supabase helper `getServerSupabase()` for route auth context.
- Prisma models impacted: `Policy`, `InsuredParty`, `Asset`.
- Component path: `src/features/account-holder/components/PolicyList.tsx`.
- Reuse `usePolicyFilters` hook; update it to client‑filter fetched data.
- Use TanStack Infinite Query pattern for pagination.

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-29 | 0.1 | Initial draft | Scrum Master |

---

## Dev Agent Record

### Agent Model Used
Claude 4 Sonnet

### File List
- `/src/app/api/account-holder/policies/list/route.ts` - New API route for listing account holder policies
- `/src/features/account-holder/hooks/usePolicies.ts` - New hooks for policy data fetching and filtering
- `/src/features/account-holder/components/policy-list.tsx` - Refactored component to use server data

### Completion Notes
- ✅ Implemented secure API route with Supabase authentication and RLS enforcement
- ✅ Added comprehensive query parameters for filtering, searching, and pagination
- ✅ Created TanStack Query hooks for data fetching with proper error handling
- ✅ Refactored PolicyList component to consume server data instead of mock data
- ✅ Maintained all existing UI functionality while switching to real data
- ✅ Added loading and error states for better UX
- ✅ Implemented proper data transformation for component compatibility

### Debug Log References
- No critical issues encountered during implementation
- Fixed TanStack Query v4 API compatibility issues
- Resolved TypeScript linting errors for undefined object properties

### Change Log
- Created new API route with comprehensive filtering and pagination
- Implemented secure data fetching with user authentication
- Replaced mock data with real database queries
- Added proper error handling and loading states
