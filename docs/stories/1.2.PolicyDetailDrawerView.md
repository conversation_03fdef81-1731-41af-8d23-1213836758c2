# Story 1.2: PolicyDetailDrawerView

## Status
Ready for Review

## Story
**As a** Account Holder,
**I want** open a policy in a slide‑out drawer to view full details,
**so that** I can review coverage and specific information without leaving the list

## Acceptance Criteria
1. Clicking a policy opens a read‑only drawer containing all policy fields.
2. Drawer reuses broker component with broker‑only actions disabled.
3. Drawer renders correctly on mobile and desktop screens.
4. Drawer closed retains list scroll/filters state.
5. Drawer shows correct fields for each status.
6. Escape key or close icon closes drawer.

## Tasks / Subtasks
- [x] Generalize `PolicyDetailsDrawer` component for multi‑context use (AC:2,5)
  - [x] Extract context props
  - [x] Disable broker mutations in account-holder mode
- [x] Integrate drawer trigger in `PolicyCard` (AC:1,4)
  - [x] Manage selected policy ID via React context or local state
  - [x] Preserve scroll position via component state or URL hash
- [x] Responsive UI tweaks (AC:3)
  - [x] Ensure Tailwind breakpoints
  - [x] Scrollable content inside drawer

## Dev Agent Record

### Agent Model Used
Claude 4 Sonnet

### Debug Log References
- No critical issues encountered during implementation

### Completion Notes
- ✅ Successfully generalized PolicyDetailsDrawer component with mode prop for broker/account-holder differentiation
- ✅ Implemented data masking for sensitive information in broker mode
- ✅ Integrated drawer trigger into PolicyCard component, replacing static link with interactive button
- ✅ Added responsive UI improvements with proper mobile breakpoints and overflow handling
- ✅ All acceptance criteria met: drawer opens from policy card, shows appropriate data based on user type, responsive design

### File List
- `/src/components/shared/PolicyDetailsDrawer.tsx` (created)
- `/src/features/brokers/components/policy-details-drawer.tsx` (modified)
- `/src/features/policies/components/policy-card.tsx` (modified)

## Dev Notes
- Component path: `src/components/shared/PolicyDetailsDrawer.tsx`.
- Prop `mode="account-holder"` toggles read-only state.
- Avoid extra API calls; reuse list data via context.
- Ensure ARIA roles and focus trap for accessibility.


## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-29 | 0.1 | Initial draft | Scrum Master |
| 2025-07-30 | 0.2 | Add scroll position preservation | Scrum Master |
| 2025-07-31 | 1.0 | Complete implementation with responsive design | Dev Agent |
