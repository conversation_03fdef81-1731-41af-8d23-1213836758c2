# 3.3 UI Consistency Checklist
- Language: Spanish  
- Responsive design  
- Component patterns (status badges, buttons)  
- Accessibility (keyboard nav, ARIA)

## 3.4 Policy Card Display Rules by Status
| Policy Status | Primary Label / Data Fields | Action Button |
|--------------|----------------------------|---------------|
| **DRAFT** | • **Primary label:** *Pending verification*<br>• **Description:** “Your policy is going through our verification process. As soon as validation is complete you will have full access to its data.”<br>• **Status badge:** DRAFT | **View details** (disabled) |
| **RENEW_SOON** | • Vehicle: _Make, Model, Trim, Year_ (e.g. **Seat Ibiza 2018**)<br>• Insurer: **Seguros Bilbao**<br>• Product type: **Third‑party + extras**<br>• Policyholder name: **<PERSON><PERSON><PERSON>**<br>• Policy number: **00002111ESP039003**<br>• Premium: **€845.20 / year**<br>• Coverage count: **12**<br>• Expires: **13 Aug 2025**<br>• Status badge: RENEW_SOON | Dynamic:<br>• **View active auction** – _if policy already linked to an auction_<br>• **Renew** – _if no auction exists; launches a new auction_ |
| **ACTIVE** | Same fields as RENEW_SOON but with current status badge **ACTIVE** and expiry date in the future | **View details** |
| **EXPIRED** | Same fields as ACTIVE but with status badge **EXPIRED** and expiry date in the past | **View details** |
| **REJECTED** | • **Primary label:** *Rejected*<br>• **Description:** “The uploaded document is not a valid policy or cannot be read clearly. Please upload a correct and legible copy to continue.”<br>• **Status badge:** REJECTED | **View details** (disabled) |


## 3.5 Policy Detail Drawer – Data Sections

| Section | Required Fields |
|---------|-----------------|
| **1. Policy Information** | Policy number • Insurer • Product name • Start date • End date • Policy type • Annual premium |
| **2. Insured Parties** | For **each** party: `fullName`, `dni/nie/cif`, `role`, `gender`, `email`, `phone`, `dateOfBirth`.<br>If role = **Policyholder** → also show: `address`, `postalCode`, `regionName`, `country`.<br>If role = **Principal / Secondary Driver** → also show: `drivingLicenceNumber`, `licenceIssuedOn` |
| **3. Asset Information** | For vehicles: `plate`, `firstRegistrationDate`, `make`, `model`, `version`, `manufacturingYear`, `vehicleType`, `fuelType`, `vin`, `power`, `seats`, `usageType`, `garageType`, `annualKm`, `leasing`. For non‑vehicle assets show the generic fields available (`assetType`, `description`, etc.). |
| **4. Coverages** | Coverage title • Limit (numeric) • Description |

---
