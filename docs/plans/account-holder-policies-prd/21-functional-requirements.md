# 2.1 Functional Requirements

| ID | Requirement |
|----|-------------|
| **FR1** | Policies list page must fetch and display live data via secure server‑side call. |
| **FR2** | Clicking a policy opens a read‑only detail drawer (reused component). |
| **FR3** | Drawer shows asset, coverage, insurer, premium, insured parties, etc. |
| **FR4** | Support all status values (DRAFT, ACTIVE, RENEW_SOON, EXPIRED). |
| **FR5** | Prisma schema updated: many‑to‑many join table `PolicyInsuredParty`; generic `Asset` plus auxiliary `VehicleDetails`; policy‑auction link; new `AdminProfile` model. |
| **FR6** | Only RENEW_SOON policies link to auctions workflow. |
| **FR7** | All data ops occur server‑side with Supabase Auth. |
