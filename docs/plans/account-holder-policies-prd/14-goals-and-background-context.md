# 1.4 Goals and Background Context

## 1.4.1 Goals
- **Complete “Mis Pólizas” with real data.**  
- Provide a full policy detail drawer view.  
- Support all policy status states (DRAFT, ACTIVE, RENEW_SOON, EXPIRED).  
- **Expand Policy Data Model** (multi‑insured, generic assets, policy‑auction link).  
- Enforce secure, server‑side data operations (Supabase Auth).  
- Maintain compatibility & consistency with existing architecture.

## 1.4.2 Background
The current hard‑coded list misleads users and limits expansion beyond vehicle insurance. Real data, richer models, and renewal‑auction linkage are required to unlock full marketplace value.
