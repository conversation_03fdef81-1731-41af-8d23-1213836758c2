# 1.1 Existing Project Overview

## 1.1.1 Analysis Source
IDE‑based fresh analysis of current Zeeguros project files and user‑provided details.

## 1.1.2 Current Project State
Zeeguros is an existing role-based Next.js 15+ monorepo application using Supabase (PostgreSQL) for data and Prisma as the ORM. The system has distinct areas for different user roles (<PERSON><PERSON><PERSON> Holder, <PERSON><PERSON><PERSON>, <PERSON><PERSON>) implemented via role-based routing and a “screaming architecture” feature organization.

Currently, the Account Holder portal includes a **“Mis Pólizas”** (My Policies) page where the user can see a list of their insurance policies, with filter and search functionalities. **However, this feature is incomplete:** it displays static mock data instead of real policy data from the database.

The primary purpose of this enhancement is to integrate real backend data into the Account Holder’s policies page and extend the policy data model to support upcoming needs, ensuring other parts of the system (like Broker views) remain unaffected.
