Okay, quiero que me ayudes a crear un nuevo PRD con historias de usuario que logren los siguientes objetivos en el feature del account-holder, denominado "Policies" 


1. Tarjetas de Mis Pólizas

* Actualmente el componente src/features/account-holder/components/policy-list.tsx. Funciona a través de mockup data. Esto debemos de eliminarlo y permitir que se muestre data directamente desde el backend.

* Muy probablemente no haya endpoints para consumir estos datos, así que se debe considerar el desarrollo de endpoints para que el account holder pueda consumir los datos de sus pólizas.

* Los datos que deben mostrar las tarjetas son:

- 1.1. Cuando es una póliza con estado “Borrador” (DRAFT)
Label principal: Pendiente de verificación
Descripción: Tu póliza está pasando por nuestro proceso de verificación. En cuanto la validación finalice, tendrás acceso completo a todos sus datos.
Estado: “Borrador”
Botón de acción: “Ver detalles” | (deshabilitado)

- 1.2. <PERSON><PERSON><PERSON> es una póliza con estado “Renovar pronto” (RENEW_SOON)
Marca, Modelo, versión y año del coche/moto: Seat Ibiza (2018)
Aseguradora: Seguros Bilbao
Tipo de Seguro: Terceros Ampliado
Nombre del Tomador: Pepe Gómez Morales
Número de la Póliza: 00002111ESP039003
Prima: 845,20€/año
Cantidad de coberturas: 12
Vence: 13/08/2025
Estado: “Renovar pronto”
Botón de acción: “Renovar” | "Ver Subasta activa" Este botón debe de ser dinámico ya que si la póliza está en subasta, debe de mostrar "Ver Subasta activa" y si no, debe de mostrar "Renovar" y permitir crear una subasta.

- 1.3. Cuando es una póliza con estado “Activa” (ACTIVE)
Marca, Modelo, versión y año del coche/moto:Toyota Corolla (2020)
Aseguradora: Mapfre
Tipo de Seguro: Todo Riesgo Premium
Nombre del Tomador: Pepe Gómez Morales
Número de la Póliza: 00002108ESP038991
Prima: 1915,88€/año
Cantidad de coberturas: 12
Vence: 21/08/2025
Estado: “Activa”
Botón de acción: “Ver detalles”

- 1.4. Cuando es una póliza con estado “Expirada” (EXPIRED)
Marca, Modelo, versión y año del coche/moto: Opel Corsa (2017)
Aseguradora: Línea Directa
Tipo de Seguro: Terceros
Nombre del Tomador: Pepe Gómez Morales
Número de la Póliza: 00002114ESP039006
Prima: 654,89 €/año
Cantidad de coberturas: 12
Vence: 31/05/2023
Estado: “Expirada”
Botón de acción: “Ver detalles”

- 1.5. Cuando es una póliza con estado “Rechazada” (REJECTED)
Label principal: Rechazada
Descripción: El documento cargado no coincide con una póliza válida o no se puede leer con claridad. Vuelve a subir una copia correcta y legible para continuar.
Estado: “Rechazada”
Botón de acción: “Ver detalles” | (deshabilitado)

*******************************************************************************

1. VISTA DETALLE DE TARJETAS
Debemos de reutilizar el componente /src/features/auctions/components/policy-details-drawer.tsx como Vista detalle en las tarjetas de src/features/account-holder/components/policy-list.tsx.

* Muy probablemente no haya endpoints para consumir el resto de datos, así que se debe considerar el desarrollo de endpoints para que el account holder pueda consumir todos los datos de sus pólizas.

Hay que tomar en cuenta que este componente lo usarán tanto el broker como el account holder, así que se debe considerar su rehusabilidad respetando el screaming architecture, DRY principles y también las convenciones declaradas en docs/architecture.

Hay que tomar en cuenta que este drawer solo maneja "Tomador, debe actualizarse para que maneje "Partes Aseguradas". En el ejemplo de a continuación te detallo que se debe mostrar:

Los datos a mostrar te lo muestro con ejemplos:

1. Información de Póliza:
Número de Póliza: 231231232AVADCS
Aseguradora: MAFRE
Producto: Seguro de Coche
Fecha de Inicio: 02/12/24
Fecha de Vencimiento: 02/12/25
Tipo de Póliza: Todo Riesgo con Franquicia
Prima Anual: 420€/mes

1. Partes Aseguradas:
* Pueden haber 1 o más partes aseguradas, pero los datos a mostrar son:

fullName: Luis Galvez
dni/nie/cif: 12345678Z
Rol: Tomador
Género: Masculino
Correo: <EMAIL>
Teléfono: +34 661 000 123
Fecha Nacimiento: 01/01/1980
si es tomador, también mostrar:
address
postalCode
regionName
country

* para los otros roles:
fullName
dni
gender

* Si es Conductor Principal o secundario mostrar
Licencia de conducir
Emisión del carnet primera vez

3. Vehículo:
- Matrícula: 1234ABC
- Fecha de 1 matriculación: 01/01/2020
- Marca: Honda
- Modelo: Acura
- Versión: Turbo
- Año de fabricación: 2020
- Tipo de Vehículo: Coche
- Combustible: Gasolina
- Bastidor/VIN: 1234567890
- Potencia: 120 CV
- Plazas: 4
- Tipo de Uso: Particular Ocasional
- Tipo de Garaje: Calle
- KM/Año: Hasta 2 000
- Vehículo Leasing: No


4. Coberturas
- Título
- Límite
- Descripción

*******************************************************************************

3. ACTUALIZAR Prisma Schema

*  Es muy probablemente que para mostrar todos estos datos, probablemente el prisma schema no soporte todos estos campos, así que se debe considerar la creación de un schema que incluya los nuevos o que no están siendo mapeados.

* También debemos de considerar lo siguiente para prisma schema:
    Muy bien, cabe destacar que actualmente nuestro modelo Prisma Schema tiene modelos principales como puede ser el Model User que a su vez se apoya de BrokerProfile, AccountHolderProfile pero por ejemplo también vamos a tener un tercer tipo de usuario que son los Admins entonces debemos manejar también su perfil esto el Prisma Schema no lo está considerando.

* Por otro lado me gustaría también darte a entender que nuestro esquema de datos debe de soportar no solo polizas de coche y moto que es lo que manejamos actualmente, sino que debería de poder soportar otro tipo de polizas como puede ser por ejemplo una de vida, de hogar, etc. entonces la idea que yo tengo y la premisa es que el usuario que se registra en la plataforma de seguros es un Account Holder y este puede subir polizas y esas polizas y a su vez estás tienen:
   - Información sobre la poliza en general, esto ya se cubre en parte.
   - Partes aseguradas o Insured Parties
   - Un Activo asegurado, pero actualmente ese modelo "Asset" está muy pensado solo para manejar datos de vehículos, esto debería de moverse a un modelo auxiliar.
   - Las coberturas de la póliza.

* Subasta: hay que considerar que las pólizas están asociadas a una subasta y esta subasta sólo puede tener una póliza asociada a ella. Las subastas tienen duración. En principio, para el MVP vamos a trabajar con subastas de 48 y 72 horas (días laborables, de lunes-viernes. No cuentan los fines de semana.) También las subastas pueden recibir bids, que son los quotes que envían los brokers, y el account holder tiene que ser capaz de elegir a tres ganadores, y estos tres ganadores se eligen al terminar la subasta. Los cuales el sistema les va a notificar por correo que han sido ganadores. Entonces esto es algo que el Prisma Schema actual no contempla.

****************************************************************

Correcciones

Dentro de los criterios de aceptación para la historia de usuario existen Policy Schema and Integrations. Necesitamos hacer unas modificaciones empezando por el modelo Insured Parties y Policy tienen una relación Many to Many. Por otro lado, el modelo Asset. Actualmente no hay datos que estén utilizando los campos de vehículo porque es un sistema que está en desarrollo. Por lo tanto, debemos de modificar el modelo Asset para que tenga una estructura más genérica a lo que es un Asset que puede ser una casa, puede ser un vehículo, un local comercial, una persona, etc. Mientras que todos los fields de vehículos deben de ser migrados a un nuevo modelo que funcionaría como una tabla/modelo auxiliar (sugiere un nombre) para el modelo principal de Asset.

Una parte importante también es que debe de considerar dentro de las tareas un modelo que sirva para gestionar lo que vendrían a ser el AdminProfile, así como está el AccountHolderProfile o el BrokerProfile. Esa tarea no está creada. Por otro lado, es importante que las tareas no deban de ser, por ejemplo, crear el modelo Insured Party. Ese modelo existe, por lo tanto, lo que debe de hacerse es, dado los campos que se esperan del lado del Front, o sea, los datos a mostrar, ya sea para las tarjetas de "Mis Pólizas" o ya sea para el Drawer, por favor, lo que debe de ser la tarea es añadir esos campos faltantes (si es que hace falta) a los modelos que ya existen, que son Insured Parties y Assets, o cualquiera existente. Para eso, primero verificar el archivo Prisma Schema.

La situación actual del proyecto es Tenemos como deadline Terminar Estos features Más Otros más que aún hay que definir Y sintetizar del lado del broker Del lado del admin Por lo tanto me gustaría enfocarme En que Construyamos funcionalidad Y estoy dispuesto a Sacrificar un poco de calidad Por lo tanto Las pruebas de testing No hay un framework aún de pruebas El testing que se hace es manual de mi lado Entonces Yo por favor quisiera que Me ayudes a Desactivar Los criterios de aceptación Y tareas Hacer pruebas Eso ahora mismo no No es necesario en el flujo Porque estaríamos añadiendo más complejidad De la que necesitamos Necesitamos es Tener un producto funcional Más no así perfecto Por otro lado También desactiva La necesidad de un CI/CD Porque no hace falta tampoco Continuous integration Ahora mismo no hace falta Lo más importante es tener Funcionalidad.

En algunas de las Stories hay una parte que se menciona sobre Zustand para manejar los estados, pero como podrás observar en el package.json no se menciona. Así que por favor te pediré que actualices las historias, el PRD, y también actualiza el documento de arquitectura, porque Zustand no lo estamos utilizando. 